package com.ict.ycwl.pickup.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.auth0.jwt.JWT;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pickup.mapper.*;
import com.ict.ycwl.pickup.pojo.dto.Address;
import com.ict.ycwl.pickup.pojo.dto.PickupUserDto;
import com.ict.ycwl.pickup.pojo.entity.*;
import com.ict.ycwl.pickup.pojo.request.pickupUser.*;
import com.ict.ycwl.pickup.pojo.vo.MapVo;
import com.ict.ycwl.pickup.pojo.vo.pickupLocation.PickupLocationVo;
import com.ict.ycwl.pickup.pojo.vo.pickupLocation.SiteStoreVo;
import com.ict.ycwl.pickup.pojo.vo.pickupUser.PickupUserSearchDownBox;
import com.ict.ycwl.pickup.pojo.vo.pickupUser.PickupUserVo;
import com.ict.ycwl.pickup.service.PickupUserService;
import com.ict.ycwl.pickup.utils.AddressParser;
import com.ict.ycwl.pickup.utils.ExcelUtil;
import com.ict.ycwl.pickup.utils.NumberChineseFormatterUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.ml.clustering.DoublePoint;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.ict.ycwl.pickup.service.impl.PickupLocationServiceImpl.formatFileSize;


@Service
@Slf4j
public class PickupUserServiceImpl extends ServiceImpl<PickupUserMapper, PickupUser> implements PickupUserService, ReadListener<PickupUserImportRequest> {


    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;

    private int num;
    /**
     * 缓存的数据
     */
    private List<PickupUserImportRequest> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    /**
     * 假设这个是一个DAO，当然有业务逻辑这个也可以是一个service。当然如果不用存储这个对象没用。
     */

    @Autowired
    private PickupUserMapper pickupUserMapper;

    @Autowired
    private FileImportLogsMapper fileImportLogsMapper;

    @Autowired
    private PickupUserParameterMapper pickupUserParameterMapper;

    @Autowired
    private PickupLocationMapper pickupLocationMapper;

    @Autowired
    private SiteStoreMapper siteStoreMapper;

    @Autowired
    private DistMapper distMapper;

    @Autowired
    private SiteSelectionMapper siteSelectionMapper;

    @Autowired
    private PickupUserImportMapper pickupUserImportMapper;

    @Value("${file.DOWNLOAD_PATH}")
    private String DOWNLOAD_PATH;

    //校验数据是否合法
    private boolean flag;


    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     *
     * @param demoDAO
     */

    @Autowired
    public PickupUserServiceImpl(PickupUserMapper demoDAO) {
        this.pickupUserMapper = demoDAO;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(PickupUserImportRequest data, AnalysisContext context) {
        cachedDataList.add(data);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        if (headMap.size() == 6) {
            boolean headValid = true;
            for (Map.Entry<Integer, ReadCellData<?>> entry : headMap.entrySet()) {
                Integer key = entry.getKey();
                String value = entry.getValue().getStringValue();
                switch (key) {
                    case 0:
                        headValid = "客户编码".equals(value);
                        break;
                    case 1:
                        headValid = "客户名称".equals(value);
                        break;
                    case 2:
                        headValid = "商店名称".equals(value);
                        break;
                    case 3:
                        headValid = "负责人".equals(value);
                        break;
                    case 4:
                        headValid = "地址".equals(value);
                        break;
                    case 5:
                        headValid = "取货柜类型".equals(value);
                        break;
                    default:
                        headValid = false;
                        break;
                }
                // 如果某一列校验失败，立即终止循环
                if (!headValid) {
                    break;
                }
            }
            // 如果所有列都校验通过，则设置flag为true
            flag = headValid;
        } else {
            flag = false;
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());
        // demoDAO.save(cachedDataList);
        log.info("存储数据库成功！");
    }


    @Override
    public List<PickupUserVo> getList(PickupUserListRequest pickupUserListRequest) {
        //客户编码
        String customerCode = pickupUserListRequest.getCustomerCode();
        //商铺地址
        String storeAddress = pickupUserListRequest.getStoreAddress();
        //挡位
        String gear = pickupUserListRequest.getGear();
        //配送距离
        Double deliveryDistance = pickupUserListRequest.getDeliveryDistance();
        //取货柜类型
        String type = pickupUserListRequest.getType();
        //取货柜地址
        String pickupContainers = pickupUserListRequest.getPickupContainers();
        //颜色分块
        int color = pickupUserListRequest.getColor();
        //所属打卡点
        String accumulationName = pickupUserListRequest.getAccumulationName();
        Long accId=0L;
        if(StringUtil.isNotBlank(accumulationName)){
             accId = pickupUserMapper.selectAccIdByAccName(accumulationName);
        }
        if(accId ==null){
            accId=-1L;
        }
        List<PickupUserDto> pickupUsers = pickupUserMapper.MyselectList(null, customerCode, storeAddress, gear, deliveryDistance, type, pickupContainers, color, accId);


        return pickupUsers.stream()
                .map(pickupUser -> {
                    PickupUserVo vo = new PickupUserVo();
                    BeanUtils.copyProperties(pickupUser, vo);
                    int color1 = pickupUser.getColor();
                    if (color1 == 5) {
                        vo.setPickupContainers("");
                    }
                    String gear1 = pickupUser.getGear();
                    if (gear1 != null) {
                        String substring = gear1.substring(0, gear1.length() - 1);
                        int i = NumberChineseFormatterUtils.chineseToNumber(substring);
                        if (i >= 25) {
                            if (color1 == 3) {
                                vo.setColor(1);
                            } else if (color1 == 4) {
                                vo.setColor(2);
                            }
                        }
                    }
                    return vo;
                }).sorted(new Comparator<PickupUserVo>() {
                    @Override
                    public int compare(PickupUserVo o1, PickupUserVo o2) {
                        // 定义 color 的优先级（数值越小，优先级越高）
                        Map<Integer, Integer> colorPriority = new HashMap<>();
                        colorPriority.put(2, 1);  // 2 最优先
                        colorPriority.put(4, 2);  // 4 其次
                        colorPriority.put(3, 3);  // 3 接着
                        colorPriority.put(1, 4);  // 1 最后

                        // 获取两个对象的 color 优先级
                        int priority1 = colorPriority.getOrDefault(o1.getColor(), Integer.MAX_VALUE);
                        int priority2 = colorPriority.getOrDefault(o2.getColor(), Integer.MAX_VALUE);
                        // 比较优先级
                        return Integer.compare(priority1, priority2);
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public Page<PickupUserVo> getPage(Integer pageNum, Integer pageSize, List<PickupUserVo> pickupUserVos) {
        //根据商铺颜色排序，然有颜色的放在前面
        /*pickupUsers.sort((o1, o2) -> {
            return o2.getColor()-o1.getColor();
        });*/
        Page<PickupUserVo> page = new Page<>(pageNum, pageSize);
        long fromIndex = (long) (pageNum - 1) * pageSize;
        long toIndex = Math.min(fromIndex + pageSize, pickupUserVos.size());
        List<PickupUserVo> resList = new ArrayList<>();
        if (fromIndex < pickupUserVos.size()) {
            resList = pickupUserVos.subList((int) fromIndex, (int) toIndex);
        }
        page.setRecords(resList);
        page.setTotal(pickupUserVos.size());
        return page;
    }

    @Override
    @Transactional
    public String reCalculate(RecalculateRequest recalculateRequest) {
        PickupUserParameter pickupUserParameter = new PickupUserParameter();
        //权值
        double weights = 0;
        //获取计算的权值
        //客户档位
        Double gear = recalculateRequest.getGear();
        //道路等级
        Double roadGrade = recalculateRequest.getRoadGrade();
        //平均送货距离
        Double avgDistance = recalculateRequest.getAvgDistance();
        //定级参数
        Double levelParam = recalculateRequest.getLevelParam();
        PickupUserParameter pickupUserParameter1 = pickupUserParameterMapper.selectById(1);
        recalculateRequest.setExcludeGear(pickupUserParameter1.getExcludeGear());
        //大于等于这个档位的取货户不参与计算
        int excludeGear = recalculateRequest.getExcludeGear();


        //填充参数对象
        pickupUserParameter.setId(1);
        pickupUserParameter.setGear(gear);
        pickupUserParameter.setRoadGrade(roadGrade);
        pickupUserParameter.setAvgDistance(avgDistance);
        pickupUserParameter.setLevelParam(levelParam);
        pickupUserParameter.setExcludeGear(excludeGear);

        //0将store表中所有的商铺数据以覆盖的方式导入pickUser表中
        //将没有加锁的数据删除
        int delete = pickupUserMapper.delete(new QueryWrapper<PickupUser>().eq("locks", 0));
        System.out.println("删除的数据的条数: " + delete);
        //将商铺表中的数据复制到pickupUser表中
        int copyNum = pickupUserMapper.copyByStore();
        System.out.println("从store表中复制过来的数据的条数：" + copyNum);
        //调用存储过程计算距离
        pickupUserMapper.calculateDeliveryDistance();
        //1.根据给定权值，编写成sql语句
        System.out.println("计算成功");

        //查询所有的档位信息，获取最大档位值和最小档位值，为01标准化做准备
        QueryWrapper<PickupUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct gear,delivery_distance as deliveryDistance");
        List<PickupUser> pickupUsers1 = pickupUserMapper.selectList(queryWrapper);
        int maxGear = Integer.MIN_VALUE;
        int minGear = Integer.MAX_VALUE;
        double maxDistance = Double.MIN_VALUE;
        double minDistance = Double.MAX_VALUE;
        for (PickupUser pickupUser : pickupUsers1) {
            if (pickupUser == null || pickupUser.getGear() == null) {
                continue;
            }
            String gear1 = pickupUser.getGear();
            String substring = gear1.substring(0, gear1.length() - 1);
            int i = NumberChineseFormatterUtils.chineseToNumber(substring);
            if (maxGear < i) {
                maxGear = i;
            }
            if (minGear > i) {
                minGear = i;
            }
            double deliveryDistance = pickupUser.getDeliveryDistance();
            if (maxDistance < deliveryDistance) {
                maxDistance = deliveryDistance;
            }
            if (minDistance > deliveryDistance) {
                minDistance = deliveryDistance;
            }
        }

        //查询所有数据,只计算没有加锁的
        List<PickupUser> pickupUsers = pickupUserMapper.selectList(new LambdaQueryWrapper<PickupUser>().eq(PickupUser::getLocks, 0));
        for (PickupUser pickupUser : pickupUsers) {
            String gear1 = pickupUser.getGear();
            //配送距离
            double deliveryDistance = pickupUser.getDeliveryDistance();
            if (StringUtil.isNotBlank(gear1) && StringUtil.isNotBlank(pickupUser.getRoadGrade())) {
                //处理客户挡位
                String substring = gear1.substring(0, gear1.length() - 1);
                //数字客户档位
                int gearNum = NumberChineseFormatterUtils.chineseToNumber(substring);
                int n1 = gearNum;
                //跳过大于等于这个档位的取货户
                if(n1>=excludeGear){
                    continue;
                }
                double gearNum1 = 1 - (double) (gearNum - minGear) / (maxGear - minGear);
                //道路等级0城区1乡镇
                int parseInt = Integer.parseInt(pickupUser.getRoadGrade());
                double DparesInt = 0;
                if (parseInt == 0) {
                    DparesInt = 0.25;
                } else {
                    DparesInt = 0.75;
                }
                //01标准化处理配送距离
                deliveryDistance = (deliveryDistance - minDistance) / (maxDistance - minDistance);
                if (n1 >= excludeGear) {
                    weights = 0;
                } else {
                    weights = gearNum1 * gear + deliveryDistance * avgDistance + roadGrade * DparesInt;
                }
                String formattedValue = String.format("%.3f", weights);
                double v = Double.parseDouble(formattedValue);
                pickupUser.setWeights(v);
            }
        }

        int num = 0;
        try {
            for (PickupUser pickupUser : pickupUsers) {
                if (pickupUser.getWeights() >= levelParam) {
                    //检查是否已经分配
                    pickupUser.setColor(3);
                } else {
                    pickupUser.setColor(1);
                }
                num += pickupUserMapper.updateById(pickupUser);
            }
            //num = pickupUserMapper.updateByList(pickupUsers);
            System.out.println("---------------------------" + num);
        } catch (Exception e) {
            return "计算失败";
        }
        //查询那些已分配状态的选址，如果选址在选址-商户关联表中，没有出现就设置为未分配
        List<SiteSelection> siteSelections = pickupLocationMapper.selectList(new LambdaQueryWrapper<SiteSelection>().eq(SiteSelection::getStatus, 3));
        for (SiteSelection siteSelection : siteSelections) {
            int id = siteSelection.getId();
            //根据id查询数据库，如果不存在，则设置为启用未分配
            int count = siteStoreMapper.selectCountBySiteId(id);
            if (count == 0) {
                siteSelection.setStatus(2);
                siteSelectionMapper.updateById(siteSelection);
            }
        }
        //将参数存储到数据库中
        int i = pickupUserParameterMapper.updateById(pickupUserParameter);
        //填充取货户类型
        int count = pickupUserImportMapper.updatePickupType();
        return "计算成功";
    }

    @Override
    public String importExcel(File file, String authorization, String beijingTime) {
        String fileName = file.getPath();
        System.out.println("fileName: " + fileName);
        // 这里默认每次会读取100条数据 然后返回过来 直接调用使用数据就行
        // 具体需要返回多少行可以在`PageReadListener`的构造函数设置
        //收集成功的结果
        ArrayList<PickupUserImportRequest> success_pickupUserImportRequests = new ArrayList<>();
        //收集全部结果
        ArrayList<PickupUserImportRequest> allPickupUserImportRequests = new ArrayList<>();
        allPickupUserImportRequests.add(new PickupUserImportRequest());


        EasyExcel.read(fileName, PickupUserImportRequest.class, new PageReadListener<PickupUserImportRequest>(dataList -> {
            for (PickupUserImportRequest excel : dataList) {
                PickupUserImportRequest pickupUserImportRequest = new PickupUserImportRequest();
                ArrayList<String> errorInfo = new ArrayList<>();

                //处理客户编码
                String customerCode = excel.getCustomerCode();
                if (!StringUtil.isNotBlank(customerCode)) {
                    errorInfo.add("客户编码为空");
                } else {
                    //查询客户编码是否存在
                    int count = pickupUserMapper.checkCustomerCode(customerCode);
                    if (count == 0) {
                        errorInfo.add("客户编码不存在");
                    } else {
                        pickupUserImportRequest.setCustomerCode(customerCode);
                    }
                }
                //处理客户名称
                String contactName = excel.getContactName();
                if (!StringUtil.isNotBlank(contactName)) {
                    errorInfo.add("客户名称为空");
                } else {
                    //查询客户名称是否存在
                    pickupUserImportRequest.setContactName(contactName);
                }

                //处理负责人
                String customerManagerName = excel.getCustomerManagerName();
                if (!StringUtil.isNotBlank(customerManagerName)) {
                    errorInfo.add("负责人为空");
                } else {
                    //查询负责人是否存在
                    pickupUserImportRequest.setCustomerManagerName(customerManagerName);
                }
                //处理地址
                String storeAddress = excel.getStoreAddress();
                if (!StringUtil.isNotBlank(storeAddress)) {
                    errorInfo.add("地址为空");
                } else {
                    //查询地址是否存在
                    pickupUserImportRequest.setStoreAddress(storeAddress);
                }
                //处理取货柜类型
                String pickupType = excel.getPickupType();
                if (!StringUtil.isNotBlank(pickupType)) {
                    errorInfo.add("取货柜类型为空");
                } else {
                    //查询取货柜类型是否存在
                    pickupUserImportRequest.setPickupType(pickupType);
                }

                //设置状态
                String errors = "";
                if (errorInfo.size() != 0) {
                    //设置详细信息
                    excel.setConclusion("失败");
                    errors = String.join("-", errorInfo);
                } else {
                    PickupUserImport pickupUserImport = new PickupUserImport();
                    BeanUtils.copyProperties(pickupUserImportRequest, pickupUserImport);
                    //将成功数据插入到数据库中
                    //int count = pickupUserMapper.updatePickupTypeByCustomerCode(customerCode, pickupType);
                    //只覆盖现有客户，不新增客户
                    //查询是否存在相同客户编号的记录，如果有的话就更新
                    Long aLong = pickupUserImportMapper.selectCount(new LambdaQueryWrapper<PickupUserImport>().eq(PickupUserImport::getCustomerCode, pickupUserImport.getCustomerCode()));
                    int count = 0;
                    if (aLong > 0) {
                        count = pickupUserImportMapper.updateAllFieldsByCustomerCode(pickupUserImport);
                    } else {
                        // 客户不存在，不进行新增，标记为失败
                        errorInfo.add("客户编码在导入表中不存在，无法覆盖");
                        count = 0;
                    }
                    //将成功数据插入到数据库中
                    if (count >= 1) {
                        pickupUserImportRequest.setConclusion("成功");
                        excel.setConclusion("成功");
                        success_pickupUserImportRequests.add(pickupUserImportRequest);
                    } else {
                        pickupUserImportRequest.setConclusion("失败");
                        excel.setConclusion("失败");
                        if (errorInfo.size() == 0) {
                            errorInfo.add("添加到数据库时失败");
                        }
                        errors = String.join("-", errorInfo);
                    }
                }
                // 处理错误信息，无论成功还是失败都需要设置
                if (errorInfo.size() > 0) {
                    errors = String.join("-", errorInfo);
                    excel.setConclusion("失败");
                }
                excel.setImportDetails(errors);
                allPickupUserImportRequests.add(excel);
            }
        })).sheet().doRead();

        //将导入日志导入数据库
        FileImportLogs fileImportLogs = new FileImportLogs();
        fileImportLogs.setFileName(file.getName());
        fileImportLogs.setImportTime(beijingTime);
        String status = "";
        if (success_pickupUserImportRequests.size() != 0 && allPickupUserImportRequests.size() - 1 > success_pickupUserImportRequests.size()) {
            status = "部分导入成功";
            allPickupUserImportRequests.get(0).setImportDetails(String.format("共%d条记录，导入成功%d条，导入失败%d条", allPickupUserImportRequests.size() - 1, success_pickupUserImportRequests.size(), allPickupUserImportRequests.size() - success_pickupUserImportRequests.size() - 1));
        } else if (success_pickupUserImportRequests.size() == 0) {
            status = "全部导入失败";
            allPickupUserImportRequests.get(0).setImportDetails("全部导入失败");
        } else if (success_pickupUserImportRequests.size() == allPickupUserImportRequests.size() - 1) {
            status = "全部导入成功";
            allPickupUserImportRequests.get(0).setImportDetails("全部导入成功");
        }
        String userName = JWT.decode(authorization).getClaim("userName").asString();
        fileImportLogs.setStatus(status);
        fileImportLogs.setUserName(userName);
        fileImportLogs.setFileSize(formatFileSize(file));
        fileImportLogs.setStoreOrCar("2");
        fileImportLogsMapper.insert(fileImportLogs);

        //处理日期
        String[] split = beijingTime.split(" ");
        String datePart = split[0];// 2024/10/8
        String[] split1 = datePart.split("/");
        String s = String.valueOf(Integer.parseInt(split1[1]));
        String s1 = String.valueOf(Integer.parseInt(split1[2]));
        datePart = split1[0] + "/" + s + "/" + s1;
        String timePart = split[1];
        String[] timeSplit = timePart.split(":");
        //String path = "/www/wwwroot/ycwl/ycwlms/data-management/data/Download/" + datePart + "/";
        String path = DOWNLOAD_PATH + datePart + "/";
        path += timeSplit[0] + "/" + timeSplit[1] + "/" + timeSplit[2] + "/";

        // 设置文件导出的路径
        File folder = new File(path);
        if (!folder.isDirectory()) {
            folder.mkdirs();
        }
        String fileName1 = path + file.getName();
        // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为用户表 然后文件流会自动关闭
        EasyExcel.write(fileName1, PickupUserImportRequest.class).sheet("sheet").doWrite(allPickupUserImportRequests);

        System.out.println("dui: " + success_pickupUserImportRequests);
        //如果数据有对有错则将全部数据表格导出数据库
        System.out.println("all: " + allPickupUserImportRequests);
        //更新取货户类型
        int i = pickupUserImportMapper.updatePickupType();
        return "导入成功";
    }


    @Override
    public boolean checkExportExcelFrom(File file, String contentType) {
        flag = false;
        EasyExcel.read(file.getPath(), PickupUserImportRequest.class, this).sheet().doRead();
        return flag;
    }

    @Override
    public int myUpdateById(PickupuserUpdateRequest updateRequest) {
        //1.根据id查询
        int id = updateRequest.getId();
        PickupUser pickupUser = pickupUserMapper.selectById(id);
        //2.替换数据
        Double dist = updateRequest.getDeliveryDistance();
        if (dist != null) {
            pickupUser.setDeliveryDistance(dist);
        }
        String type = updateRequest.getType();
        if (StringUtil.isNotBlank(type)) {
            //创建取货类型对象，将数据存储到数据库中
            PickupUserImport pickupUserImport = new PickupUserImport();
            BeanUtils.copyProperties(pickupUser, pickupUserImport);
            pickupUserImport.setPickupType(type);
            //根据客户编码查询是否存在相同的数据，如果存在就只修改取货柜类型
            PickupUserImport pickupUserImport1 = pickupUserImportMapper.selectByCustomerCode(pickupUser.getCustomerCode());
            if (pickupUserImport1 != null) {
                pickupUserImport1.setPickupType(type);
                pickupUserImportMapper.updateById(pickupUserImport1);
            } else {
                //如果不存在就插入数据到数据库中
                pickupUserImportMapper.insert(pickupUserImport);
            }
            pickupUser.setType(type);
        }
        String pickupContainers = updateRequest.getPickupContainers();
        if (StringUtil.isNotBlank(pickupContainers)) {
            //根据名称数据库中查询是否存在该名称的选址
            SiteSelection siteSelection = siteSelectionMapper.selectByName(pickupContainers);
            //存在选址，为该选址关联当前商户
            if (siteSelection != null) {
                int siteId = siteSelection.getId();
                int puId = pickupUser.getId();
                SiteStore siteStore = new SiteStore();
                siteStore.setSiteSelectionId(siteId);
                siteStore.setStoreId(puId);
                //根据商铺id判断是否存在重复记录
                SiteStore siteStore1 = siteStoreMapper.selectByStoreId(puId);
                if (siteStore1 != null) {
                    siteStore1.setSiteSelectionId(siteId);
                    siteStoreMapper.updateById(siteStore1);
                } else {
                    siteStoreMapper.insert(siteStore);
                }
            }
            //不存在即为自定义选址,不进行特殊处理
            //修改取货户状态为已分配
            if (pickupUser.getColor() == 1) {
                pickupUser.setColor(2);
            } else if (pickupUser.getColor() == 3) {
                pickupUser.setColor(4);
            }
            pickupUser.setPickupContainers(pickupContainers);
        }
        int locks = updateRequest.getLocks();
        pickupUser.setLocks(locks);
        //3.更新数据库
        int i = pickupUserMapper.updateById(pickupUser);
        return i;
    }

    @Override
    public String export(HttpServletResponse response) throws IOException {
        String fileName = "取货户分析数据导出结果.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        PickupUserParameter pickupUserParameter = pickupUserParameterMapper.selectById(1);
        ArrayList<PickupUserParameter> pickupUserParameters = new ArrayList<>();
        pickupUserParameters.add(pickupUserParameter);

        ExcelWriter writer = ExcelUtil.createExport(response, fileName);
        ExcelUtil.writeSheet(writer, "参数列表", pickupUserParameters, PickupUserParameter.class);

        List<PickupUser> pickupUsers = pickupUserMapper.selectList(null);
        ArrayList<PickupuserExportRequest> pickupuserExportRequests = new ArrayList<>();
        for (PickupUser pickupUser : pickupUsers) {
            PickupuserExportRequest pe = new PickupuserExportRequest();
            pe.setCustomerCode(pickupUser.getCustomerCode());
            pe.setContactName(pickupUser.getContactName());
            pe.setStoreName(pickupUser.getStoreName());
            pe.setCustomerManagerName(pickupUser.getCustomerManagerName());
            pe.setContactPhone(pickupUser.getContactPhone());
            pe.setStoreAddress(pickupUser.getStoreAddress());
            pe.setRoadGrade(pickupUser.getRoadGrade());
            pe.setGear(pickupUser.getGear());
            pe.setDeliveryDistance(pickupUser.getDeliveryDistance());
            //如果是待分配商户，则不导出选址
            if (pickupUser.getColor() == 5) {
                pe.setPickupContainers("");
            } else {
                pe.setPickupContainers(pickupUser.getPickupContainers());
            }
            pe.setType(pickupUser.getType());
            pe.setWeights(pickupUser.getWeights());
            pickupuserExportRequests.add(pe);
        }
        ExcelUtil.writeSheet(writer, "数据列表", pickupuserExportRequests, PickupuserExportRequest.class);
        ExcelUtil.finishWrite(writer);
        return "导出成功";
    }

    @Override
    public boolean toBeAssigned(ArrayList<Integer> ids) {
        List<PickupUser> pickupUsers = pickupUserMapper.selectList(new LambdaQueryWrapper<PickupUser>().in(PickupUser::getId, ids));
        AjaxResult recalculate = recalculate(pickupUsers);
        if (recalculate.isSuccess()) {
            return true;
        }
        return false;
    }

    @Override
    public AjaxResult getDownBox() {
        PickupUserSearchDownBox pickupUserSearchDownBox = new PickupUserSearchDownBox();
        List<String> gears = pickupUserMapper.selectDownBox();
        gears.sort(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                o1 = o1.substring(0, o1.length() - 1);
                o2 = o2.substring(0, o2.length() - 1);
                int o11 = NumberChineseFormatterUtils.chineseToNumber(o1);
                int o22 = NumberChineseFormatterUtils.chineseToNumber(o2);
                return o11 - o22;
            }
        });
        pickupUserSearchDownBox.setGears(gears);
        ArrayList<String> list = new ArrayList<>();
        list.add("01");
        list.add("02");
        list.add("03");
        list.add("04");
        pickupUserSearchDownBox.setType(list);

        return AjaxResult.success(pickupUserSearchDownBox);
    }

    @Override
    public AjaxResult getParameters() {
        PickupUserParameter pickupUserParameter = pickupUserParameterMapper.selectById(1);
        return AjaxResult.success(pickupUserParameter);
    }

    @Override
    public AjaxResult getMapMarkers() {
        MapVo mapVo = new MapVo();
        List<PickupUser> pickupUsers = pickupUserMapper.selectList(new LambdaQueryWrapper<PickupUser>().in(PickupUser::getColor, 2, 3, 4));
        List<SiteSelection> siteSelections = siteSelectionMapper.selectList(null);
        // 使用 Stream API 转换 records
        List<PickupLocationVo> voList = siteSelections.stream()
                .map(siteSelection -> {
                    PickupLocationVo vo = new PickupLocationVo();
                    vo.setId(siteSelection.getId());
                    vo.setPickupName(siteSelection.getPickupName());
                    vo.setPickupAddress(siteSelection.getPickupAddress());
                    vo.setLongitude(siteSelection.getLongitude());
                    vo.setLatitude(siteSelection.getLatitude());
                    vo.setType(siteSelection.getType());
                    vo.setStatus(siteSelection.getStatus());
                    //处理商户
                    List<SiteStoreVo> siteStores = siteStoreMapper.selectBySiteId(siteSelection.getId());
                    vo.setStores(siteStores);
                    return vo;
                })
                .collect(Collectors.toList());

        mapVo.setPickupUsers(pickupUsers);
        mapVo.setPickupLocationVos(voList);
        return AjaxResult.success(mapVo);
    }

    @Override
    @Transactional
    public AjaxResult recalculate() {
        //清空所有分配,加锁的除外
        int removecount = siteStoreMapper.deleteAll();
        //将商户表中所有的选址都清空

        //查询出所有的选址信息,选取没有被禁用的
        List<SiteSelection> siteSelections = pickupLocationMapper.selectList(new LambdaQueryWrapper<SiteSelection>().ne(SiteSelection::getStatus, 1));
        //查询出所有的商铺信息(只选取所有定点取货户)
        List<PickupUser> pickupUsers = pickupUserMapper.selectList(new LambdaQueryWrapper<PickupUser>().ne(PickupUser::getLocks, 1).eq(PickupUser::getColor, 3).or().eq(PickupUser::getColor, 4));

        //遍历商铺信息，依次处理每一个商铺
        for (PickupUser pickupUser : pickupUsers) {
            //获取商户的详细地址
            String storeAddress = pickupUser.getStoreAddress();
            //解析商户地址
            Address parse = AddressParser.parse(storeAddress);
            //创建收集合适的选址
            ArrayList<SiteSelection> siteSelections1 = new ArrayList<>();

            //市
            String city = parse.getCity();
            //区/县/县级市
            String district = parse.getDistrict();
            //镇
            String town = parse.getTown();
            //村/村委会
            String village = parse.getVillage();

            //获取商铺的经纬度
            double longitude = pickupUser.getLongitude();
            double latitude = pickupUser.getLatitude();


            DoublePoint doublePoint = new DoublePoint(new double[]{longitude, latitude});

            int flag = 0;

            // 注释掉地址约束逻辑，优先考虑行程距离
            /*
            //获取选址信息
            for (SiteSelection siteSelection : siteSelections) {
                int b = 0;
                //1
                String village1 = siteSelection.getVillage();
                String town1 = siteSelection.getTown();
                String district1 = siteSelection.getDistrict();
                String city1 = siteSelection.getCity();
                if (StringUtil.isNotBlank(village) && village.equals(village1)) {
                    b = 4;
                } else if (StringUtil.isNotBlank(town) && town.equals(town1)) {
                    b = 3;
                } else if (StringUtil.isNotBlank(district) && district.equals(district1)) {
                    b = 2;
                } else if (StringUtil.isNotBlank(city) && city.equals(city1)) {
                    b = 1;
                }
                if (b > flag) {
                    flag = b;
                    //清空原有集合
                    siteSelections1.clear();
                    //有更优的选址，将选址添加到集合中
                    siteSelections1.add(siteSelection);
                } else if (b == flag) {
                    siteSelections1.add(siteSelection);
                }
            }
            */
            double shortestDistance = Double.MAX_VALUE;
            //标记哪个是距离最近的选址
            SiteSelection siteSelection = new SiteSelection();

            //如果当前商铺的经纬度为0，则在匹配集中随机挑选一个作为选址
            if (longitude == 0 || latitude == 0) {
                siteSelection = siteSelections1.get(0);
            } else {
                //直接遍历所有的选址，匹配一个最近的选址（优先考虑距离）
                // if (flag == 0) {
                    siteSelections1.addAll(siteSelections);
                // }
                //循环结束后，遍历结果集，依次计算，取一个最短距离
                for (SiteSelection selection : siteSelections1) {
                    //获取坐标
                    double longitude1 = selection.getLongitude();
                    double latitude1 = selection.getLatitude();
                    DoublePoint doublePoint1 = new DoublePoint(new double[]{longitude1, latitude1});
                    //使用高德地图API获取实际距离
                    double dist = saveDistanceInformation(doublePoint, doublePoint1, "");
                    if (dist < shortestDistance) {
                        shortestDistance = dist;
                        siteSelection = selection;
                    }
                }
            }
            //循环结束,将结果存储到数据库中
            SiteStore siteStore = new SiteStore();
            siteStore.setStoreId(pickupUser.getId());
            siteStore.setSiteSelectionId(siteSelection.getId());
            //根据商铺查询是否有同样的数据，如果存在就进行更新操作
            SiteStore s = siteStoreMapper.selectByStoreId(pickupUser.getId());
            if (s != null) {
                int i = siteStoreMapper.updateById(s);
            } else {
                //如果不存在就进行插入操作
                int insert = siteStoreMapper.insert(siteStore);
            }
            pickupUser.setPickupContainers(siteSelection.getPickupName());
            pickupUser.setDeliveryDistance(shortestDistance);
            //判断商户是否是定点取货户
            int color = pickupUser.getColor();
            if (color == 1) {
                //普通商户未分配
                pickupUser.setColor(2);
            } else if (color == 3) {
                //如果是普通商户定点取货户
                pickupUser.setColor(4);
            }
            pickupUserMapper.updateById(pickupUser);
            //将选址更新为状态1，为启用已分配状态
            siteSelection.setStatus(3);
            siteSelectionMapper.updateById(siteSelection);
        }
        return AjaxResult.success("计算成功");
    }

    @Override
    public AjaxResult updateParameters(RecalculateRequest recalculateRequest) {
        PickupUserParameter parameter = new PickupUserParameter();
        BeanUtils.copyProperties(recalculateRequest,parameter);
        parameter.setId(1);
        int i = pickupUserParameterMapper.updateById(parameter);
        if(i==1){
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.error("修改失败");
    }


    @Transactional
    public AjaxResult recalculate(List<PickupUser> pickupUsers) {
        //查询出所有的选址信息,选取没有被禁用的
        List<SiteSelection> siteSelections = pickupLocationMapper.selectList(new LambdaQueryWrapper<SiteSelection>().ne(SiteSelection::getStatus, 1));

        //遍历商铺信息，依次处理每一个商铺
        for (PickupUser pickupUser : pickupUsers) {
            //获取商户的详细地址
            String storeAddress = pickupUser.getStoreAddress();
            //解析商户地址
            Address parse = AddressParser.parse(storeAddress);
            //创建收集合适的选址
            ArrayList<SiteSelection> siteSelections1 = new ArrayList<>();

            //市
            String city = parse.getCity();
            //区/县/县级市
            String district = parse.getDistrict();
            //镇
            String town = parse.getTown();
            //村/村委会
            String village = parse.getVillage();

            //获取商铺的经纬度
            double longitude = pickupUser.getLongitude();
            double latitude = pickupUser.getLatitude();


            DoublePoint doublePoint = new DoublePoint(new double[]{longitude, latitude});

            int flag = 0;

            //获取选址信息
            for (SiteSelection siteSelection : siteSelections) {
                int b = 0;
                //1
                String village1 = siteSelection.getVillage();
                String town1 = siteSelection.getTown();
                String district1 = siteSelection.getDistrict();
                String city1 = siteSelection.getCity();
                if (StringUtil.isNotBlank(village) && village.equals(village1)) {
                    b = 4;
                } else if (StringUtil.isNotBlank(town) && town.equals(town1)) {
                    b = 3;
                } else if (StringUtil.isNotBlank(district) && district.equals(district1)) {
                    b = 2;
                } else if (StringUtil.isNotBlank(city) && city.equals(city1)) {
                    b = 1;
                }
                if (b > flag) {
                    flag = b;
                    //清空原有集合
                    siteSelections1.clear();
                    //有更优的选址，将选址添加到集合中
                    siteSelections1.add(siteSelection);
                } else if (b == flag) {
                    siteSelections1.add(siteSelection);
                }
            }
            double shortestDistance = Double.MAX_VALUE;
            //标记哪个是距离最近的选址
            SiteSelection siteSelection = new SiteSelection();

            //如果当前商铺的经纬度为0，则在匹配集中随机挑选一个作为选址
            if (longitude == 0 || latitude == 0) {
                siteSelection = siteSelections1.get(0);
            } else {
                //如果循环结束后标记还是0，则遍历所有的选址，匹配一个最近的选址
                if (flag == 0) {
                    siteSelections1.addAll(siteSelections);
                }
                //循环结束后，遍历结果集，依次计算，取一个最短距离
                for (SiteSelection selection : siteSelections1) {
                    //获取坐标
                    double longitude1 = selection.getLongitude();
                    double latitude1 = selection.getLatitude();
                    DoublePoint doublePoint1 = new DoublePoint(new double[]{longitude1, latitude1});
                    //使用高德地图API获取实际距离
                    double dist = saveDistanceInformation(doublePoint, doublePoint1, "");
                    if (dist < shortestDistance) {
                        shortestDistance = dist;
                        siteSelection = selection;
                    }
                }
            }
            //设置最近选址
            pickupUser.setPickupContainers(siteSelection.getPickupName());
            //设置最近距离
            pickupUser.setDeliveryDistance(shortestDistance);
            pickupUser.setColor(5);
            //将商铺状态设置为待分配
            pickupUserMapper.updateById(pickupUser);
        }
        return AjaxResult.success("设置成功");
    }



    // 高德地图API密钥池
    private static final String[] AMAP_API_KEYS = {
        "a123fae9da370c45984c58720bf3ac7c",
        "3acb45c690a8aed5095eff50887689f6", 
        "3729e38b382749ba3a10bae7539e0d9a"
    };
    
    private static int currentApiKeyIndex = 0;
    
    public double saveDistanceInformation(DoublePoint point1, DoublePoint point2, String apiKey) {
        // 验证坐标有效性，添加详细的错误信息
        double lon1 = point1.getPoint()[0];
        double lat1 = point1.getPoint()[1];
        double lon2 = point2.getPoint()[0];
        double lat2 = point2.getPoint()[1];
        
        if (!isValidCoordinate(lon1, lat1)) {
            System.err.println("无效的起点坐标: (" + lon1 + ", " + lat1 + ")");
            throw new RuntimeException("无效的起点坐标数据: (" + lon1 + ", " + lat1 + ")，经度范围应为73.33-135.05，纬度范围应为3.51-53.33");
        }
        
        if (!isValidCoordinate(lon2, lat2)) {
            System.err.println("无效的终点坐标: (" + lon2 + ", " + lat2 + ")");
            throw new RuntimeException("无效的终点坐标数据: (" + lon2 + ", " + lat2 + ")，经度范围应为73.33-135.05，纬度范围应为3.51-53.33");
        }
        
        // 拼接经度和纬度字符串
        String origin = point1.getPoint()[0] + "," + point1.getPoint()[1];
        String destination = point2.getPoint()[0] + "," + point2.getPoint()[1];
        
        if (origin.equals(destination)) {
            return 1;
        }
        
        // 查询数据库缓存
        List<Dist> dist1 = distMapper.selectList(new QueryWrapper<Dist>().eq("origin", origin).eq("destination", destination));
        List<Dist> dist3 = distMapper.selectList(new QueryWrapper<Dist>().eq("origin", destination).eq("destination", origin));
        if (dist1.size() > 0 && dist1.get(0).getDist() > 0) {
            // 检查缓存的距离值，如果大于1000可能是米制，需要转换为千米
            double cachedDist = dist1.get(0).getDist();
            return cachedDist > 1000 ? cachedDist / 1000.0 : cachedDist;
        }
        if (dist3.size() > 0 && dist3.get(0).getDist() > 0) {
            // 检查缓存的距离值，如果大于1000可能是米制，需要转换为千米
            double cachedDist = dist3.get(0).getDist();
            return cachedDist > 1000 ? cachedDist / 1000.0 : cachedDist;
        }
        
        // 尝试使用高德地图API获取距离
        double dist = callAmapApiWithFallback(origin, destination);
        
        // 缓存结果到数据库
        if (dist > 0) {
            Dist dist2 = new Dist();
            dist2.setDist(dist);
            dist2.setOrigin(origin);
            dist2.setDestination(destination);
            distMapper.insert(dist2);
        }
        
        return dist;
    }
    
    /**
     * 调用高德地图API获取距离
     */
    private double callAmapApiWithFallback(String origin, String destination) {
        // 尝试所有API密钥
        for (int i = 0; i < AMAP_API_KEYS.length; i++) {
            String apiKey = AMAP_API_KEYS[(currentApiKeyIndex + i) % AMAP_API_KEYS.length];
            try {
                double distance = callAmapApi(origin, destination, apiKey);
                if (distance > 0) {
                    currentApiKeyIndex = (currentApiKeyIndex + i) % AMAP_API_KEYS.length;
                    return distance;
                }
            } catch (Exception e) {
                System.out.println("API Key " + apiKey.substring(0, 8) + "... failed: " + e.getMessage());
            }
        }
        
        // 所有API密钥都失败，直接抛出异常
        throw new RuntimeException("所有高德地图API密钥都失败，无法获取距离信息");
    }
    
    /**
     * 调用高德地图API获取距离
     */
    private double callAmapApi(String origin, String destination, String apiKey) throws Exception {
        String url = "https://restapi.amap.com/v3/direction/driving";
        String requestUrl = url + "?origin=" + origin + "&destination=" + destination + 
                          "&strategy=2&number=FD08088&extensions=all&output=json&key=" + apiKey;
        
        URL urlObj = new URL(requestUrl);
        HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(10000); // 10秒连接超时
        connection.setReadTimeout(15000);    // 15秒读取超时
        
        Scanner scanner = new Scanner(connection.getInputStream());
        StringBuilder response = new StringBuilder();
        while (scanner.hasNextLine()) {
            response.append(scanner.nextLine());
        }
        scanner.close();
        
        String responseStr = response.toString();
        String status = extractJsonValue(responseStr, "status");
        
        if ("1".equals(status)) {
            return extractDistanceFromPaths(responseStr);
        } else {
            String info = extractJsonValue(responseStr, "info");
            String infocode = extractJsonValue(responseStr, "infocode");
            throw new RuntimeException("高德地图API错误 - Info: " + info + ", Infocode: " + infocode);
        }
    }
    
    /**
     * 从JSON字符串中提取指定字段的值
     */
    private String extractJsonValue(String json, String key) {
        String pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]*)\"|\"" + key + "\"\\s*:\\s*([^,}\\]\\s]+)";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(json);
        if (m.find()) {
            return m.group(1) != null ? m.group(1) : m.group(2);
        }
        return null;
    }
    
    /**
     * 从paths数组中提取距离信息
     */
    private double extractDistanceFromPaths(String json) {
        try {
            String pathsPattern = "\"paths\"\\s*:\\s*\\[([^\\]]+)\\]";
            java.util.regex.Pattern pathsP = java.util.regex.Pattern.compile(pathsPattern);
            java.util.regex.Matcher pathsM = pathsP.matcher(json);
            
            if (pathsM.find()) {
                String pathsContent = pathsM.group(1);
                String distancePattern = "\"distance\"\\s*:\\s*\"?([0-9.]+)\"?";
                java.util.regex.Pattern distanceP = java.util.regex.Pattern.compile(distancePattern);
                java.util.regex.Matcher distanceM = distanceP.matcher(pathsContent);
                
                if (distanceM.find()) {
                    // 高德地图API返回的距离单位是米，转换为km
                    double distanceInMeters = Double.parseDouble(distanceM.group(1));
                    return distanceInMeters / 1000.0;
                }
            }
        } catch (Exception e) {
            System.out.println("距离解析错误: " + e.getMessage());
        }
        return 0.0;
    }
    
    /**
     * 验证坐标是否有效（中国境内）
     */
    private boolean isValidCoordinate(double longitude, double latitude) {
        // 中国境内经纬度范围：经度73°33′至135°05′，纬度3°51′至53°33′
        return longitude >= 73.33 && longitude <= 135.05 && latitude >= 3.51 && latitude <= 53.33;
    }

    @Override
    public PickupUserVo getPickupUserWithRoute(Integer id) {
        // 根据ID查询取货户基本信息
        PickupUser pickupUser = this.getById(id);
        if (pickupUser == null) {
            return null;
        }

        // 转换为VO对象
        PickupUserVo vo = new PickupUserVo();
        BeanUtils.copyProperties(pickupUser, vo);

        // 查询路线信息
        if (StringUtil.isNotBlank(pickupUser.getCustomerCode())) {
            String routeName = pickupUserMapper.getRouteNameByCustomerCode(pickupUser.getCustomerCode());
            vo.setCustomerManagerName(routeName != null ? routeName : pickupUser.getCustomerManagerName());
        }

        return vo;
    }


}
