package com.ict.ycwl.pickup.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pickup.pojo.entity.PickupUser;
import com.ict.ycwl.pickup.pojo.request.pickupUser.PickupUserListRequest;
import com.ict.ycwl.pickup.pojo.request.pickupUser.PickupuserUpdateRequest;
import com.ict.ycwl.pickup.pojo.request.pickupUser.RecalculateRequest;
import com.ict.ycwl.pickup.pojo.vo.pickupUser.PickupUserVo;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


public interface PickupUserService extends IService<PickupUser> {
    //获取取货户分析列表数据
    List<PickupUserVo> getList(PickupUserListRequest pickupUserListRequest);
    Page<PickupUserVo> getPage(Integer pageNum, Integer pageSize, List<PickupUserVo> pickupUserVos);

    String reCalculate(RecalculateRequest recalculateRequest);


    String importExcel(File file, String authorization, String beijingTime);

    boolean checkExportExcelFrom(File file, String contentType);

    int myUpdateById(PickupuserUpdateRequest updateRequest);


    String export(HttpServletResponse response) throws IOException;

    boolean toBeAssigned(ArrayList<Integer> ids);

    AjaxResult getDownBox();

    AjaxResult getParameters();

    AjaxResult getMapMarkers();

    AjaxResult recalculate();

    AjaxResult updateParameters(RecalculateRequest recalculateRequest);

    //根据ID获取取货户详情（包含路线信息）
    PickupUserVo getPickupUserWithRoute(Integer id);

}
