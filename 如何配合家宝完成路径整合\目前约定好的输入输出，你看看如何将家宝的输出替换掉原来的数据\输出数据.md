### 1. PathPlanningResult 数据结构

```java
public class PathPlanningResult {
    /**
     * 计算状态
     */
    private boolean success;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 路径规划结果
     */
    private List<RouteResult> routes;
    
    /**
     * 计算耗时（毫秒）
     */
    private Long executionTime;
    
    /**
     * 时间均衡统计（用于评估算法效果）
     */
    private TimeBalanceStats timeBalanceStats;
}
```

### 2. RouteResult - 路线结果
```java
public class RouteResult {
    /**
     * 路线ID
     */
    private Long routeId;
    
    /**
     * 路线名称
     */
    private String routeName;
    
    /**
     * 所属中转站ID
     */
    private Long transitDepotId;
    
    /**
     * 路线包含的聚集区ID列表（按访问顺序）
     */
    private List<Long> accumulationSequence;
    
    /**
     * 路线坐标串（用于地图绘制）
     */
    private List<CoordinatePoint> polyline;
    
    /**
     * 路线总工作时长（分钟）
     */
    private Double totalWorkTime;
}
```

### 3. TimeBalanceStats - 时间均衡统计
```java
public class TimeBalanceStats {
    /**
     * 路线间时间差距统计（每个中转站下所有路线的最大时间差）
     * Key: 中转站ID, Value: 该中转站下路线间最大时间差（分钟）
     */
    private Map<Long, Double> routeTimeGapByDepot;
    
    /**
     * 中转站间时间差距统计（每个班组下所有中转站的最大时间差）
     * Key: 班组ID, Value: 该班组下中转站间最大时间差（分钟）
     */
    private Map<Long, Double> depotTimeGapByTeam;
    
    /**
     * 班组间时间差距统计（全局最大时间差）
     * Value: 所有班组间最大时间差（分钟）
     */
    private Double teamTimeGap;
}
```

### 4. CoordinatePoint - 坐标点
```java
public class CoordinatePoint {
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
}
```