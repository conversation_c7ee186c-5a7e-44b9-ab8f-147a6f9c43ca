package com.ict.ycwl.clustercalculate.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MapResultPoint {
    private ArrayList<Double> lnglat;
    private String state;
    private String name;
    private Integer style;
    private Long accumulationId;
    private String accumulation;
    private Long routeId;
    private Long areaId;
    private String remark;
    private String specialType;
    // 关联的特殊商铺列表，用于气泡浮窗显示
    private List<SpecialStore> specialStores;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpecialStore {
        private String storeName;
        private String specialType;
        private String remark;
        private Double longitude;
        private Double latitude;
    }
}
