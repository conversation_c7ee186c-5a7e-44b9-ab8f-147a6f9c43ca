<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="clustercalculate" />
        <module name="data-management" />
        <module name="common" />
        <module name="guestbook" />
        <module name="pathcalculate" />
        <module name="pickup" />
        <module name="user-service" />
        <module name="feign-api" />
        <module name="gateway" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="8" />
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="clustercalculate" options="-parameters" />
      <module name="common" options="-parameters" />
      <module name="data-management" options="-parameters" />
      <module name="feign-api" options="-parameters" />
      <module name="gateway" options="-parameters" />
      <module name="guestbook" options="-parameters" />
      <module name="pathcalculate" options="-parameters" />
      <module name="pickup" options="-parameters" />
      <module name="user-service" options="-parameters" />
    </option>
  </component>
</project>