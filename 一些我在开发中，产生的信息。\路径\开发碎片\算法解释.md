# 1.聚集区

#### k-Means算法可视化：

[可视化 K-Means 聚类 (naftaliharris.com)](https://www.naftaliharris.com/blog/visualizing-k-means-clustering/)

#### 算法步骤：

1.随机选择k个质心，k由我们指定

2.然后依次计算每个点距离每一个质心的距离，然后将他归属为最近的那个质心，直到所有点归属完毕。

3.然后对每个质心下的点，计算出一个中心点，作为下次计算的质心

4.然后又重新进行归属

5.结束条件：直到不在有新的质心产生或者达到了我们设置的迭代次数

#### 全程：

1.依次处理每个大区的商铺坐标

2.尝试不同的簇数，并记录对应的sse值

​	2.0 sse值结算过程：

​		1.将每个簇中的点，用其经度-质心经度 * 纬度-质心纬度，将	所有点的结果加起来，就是当前簇数的sse值

​	2.1 先用k-Means算法计算出簇，簇中包含簇心和簇中的坐标

2.2 遍历sse集合，用找前一项和后一项的最大的差值的那组数据，然后取后一项就是最佳的sse值。

![image-20240929211827472](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240929211827472.png)

![image-20240929211855143](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240929211855143.png)

3.再进行一遍k-means算法，计算出簇

4.一直循环计算，直到所有簇中的坐标数量达到指定的数量

5.将计算好的数据插入到数据库中



# 路径计算

### 计算数据：计算会依次计算每个中转站

#### 来源：中转站-》中转站下的聚集区

##### 路线数量：当前中转站下的车辆的5倍

1.中转站

2.分为指定数量的簇

![image-20240929225148235](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240929225148235.png)

3.





1.将xlsx文件导入数据库

2.根据配送域填充大区

2.1先填充配送域非韶关的

2.2根据地址填充韶关区域

3.剩余根据地址填充不了的计算其离哪个中转站近就填充为哪个中转站下的大区

4.所有商铺所属大区填充完毕

5.计算商铺类型是城镇还是乡村

6.根据所属大区填充商铺表中分组id

7.计算聚集区

8.计算路径
