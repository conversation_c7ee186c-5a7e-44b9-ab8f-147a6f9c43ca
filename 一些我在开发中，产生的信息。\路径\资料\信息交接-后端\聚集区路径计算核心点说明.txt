我这里简单提一下整体的思路，具体的每一个关键步骤基本上都有注释。

聚集区：核心代码是 cluster-calculate 模块的 CalculateServiceImpl 的 calculate 方法。
整体上来说就是一个类似分治的思想，可以把我们整个程序看成一棵树。树的每个节点代表一个聚集区。
最开始的数据，也就是数据库里所有的商铺，可以看成是一个包含所有商铺的最大聚集区。这个最大聚集区就是根节点。
之后，我们要做的事情，一句话就是把这个最大聚集区不断分割切小，直到所有分割出来的小聚集区满足我们的要求（商铺数最大不超过设置的数）
分割的过程，就是代码的第 64 行开始的整个 while 循环。
具体怎么分割，这里用了第三方的kmeans 库，直接调接口就可以帮我们分割，我们只要关心分割出来的结果符不符合我们要求即可。
（这里提到树的概念，但实际上代码并没有用到树这种数据结构）

路径计算：核心代码是 path-calculate 模块的 CalculateServiceImpl 的 calculate 方法。
整体上来说，用了分簇+穷举的思想。
先分簇，是因为我觉得我们求的是最短路径，而分簇可以帮我们把相邻比较近的打卡点划分在一起，效果类似。
所以要 n 条路径，那就把一个大区的打卡点分为 n 个簇。
分好簇之后，理论上来说我们就可以对每个簇里面所拥有的打卡点进行穷举计算，看看怎么排顺序才是距离最短。
但是实际上，因为每个簇里面所拥有的打卡点的数量有可能是几十个，而这种情况下穷举的时间复杂度很差，所以我们还得做下改进。
改进其实就是，对于单个簇，再把它进行分簇。可以把这二次分簇得到的结果看成是这条路径的一小段路径。
然后再把每个一小段路径进行合并，连起来，就是这单个簇所代表的一条路径了。