# 部署

## 烟草服务器信息

| 序号 | IP           | 名称                       | 登录方式 | 用户名      | 密码           | 操作系统         | CPU  | 内存 | 硬盘 |
| ---- | ------------ | -------------------------- | -------- | ----------- | -------------- | ---------------- | ---- | ---- | ---- |
| 1    | ************ | 物流送货路线规划应用服务器 | SSH      | xlghyy/root | xlghyy@8963900 | Ubuntu 22.04 LTS | 8核  | 16GB | 200G |
| 2    | ************ | 物流送货路线规划数据服务器 | SSH      | xlghsj/root | xlghsj@8963900 | Ubuntu 22.04 LTS | 8核  | 16GB | 500G |

数据库密码

![image-20241227093158632](assets/image-20241227093158632.png)

## 服务（所有更新后的服务，以gateway为例）

### 1. 镜像打包

```
docker save -o /tmp/gateway.tar gateway:1.0
```



### 2. 上传至部署服务器

### 3. 加载镜像

```
docker load -i gateway.tar
```



### 3.运行

```
// 路径替换为挂载目录（nacos配置文件中可查询，file.path）
docker run --name gateway -v /www/wwwroot/ycwl/resource/file:/www/wwwroot/ycwl/resource/file -p 8080:8080 -d gateway:1.1
```

### 

### 4. 更新Nacos配置文件、更新MYSQL数据库

MySQL：部署服务器IP:3306

nacos：

540nacos

账号：nacos

密码：ycwlnacos0807@