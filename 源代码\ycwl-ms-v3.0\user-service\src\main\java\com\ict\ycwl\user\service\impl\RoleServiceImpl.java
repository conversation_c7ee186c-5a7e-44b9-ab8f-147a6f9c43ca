package com.ict.ycwl.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.dao.OperationDao;
import com.ict.ycwl.user.dao.RoleDao;
import com.ict.ycwl.user.dao.RoleOperationDao;
import com.ict.ycwl.user.pojo.Operation;
import com.ict.ycwl.user.pojo.Role;
import com.ict.ycwl.user.pojo.RoleOperation;
import com.ict.ycwl.user.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class RoleServiceImpl extends ServiceImpl<RoleDao, Role> implements RoleService {

    @Autowired
    private RoleOperationDao roleOperationDao;

    @Autowired
    private OperationDao operationDao;

    @Autowired
    private RoleDao roleDao;

    @Override
    public AjaxResult setRoleOperation(Long roleId, String idList) {


        //将传来的id字符串存入数组
        List<String> strList = Arrays.asList(idList.split(","));

        //获取用户权限点列表的长度
        QueryWrapper<RoleOperation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id",roleId);
        List<RoleOperation> list = roleOperationDao.selectList(queryWrapper);

        //遍历数组将相对应的权限id状态设置为"1",否则设置为“0”
        for (int i = 0; i < list.size(); i++) {

            UpdateWrapper<RoleOperation> wrapper = new UpdateWrapper<>();

            for (int j = 0; j < strList.size(); j++) {

                if((list.get(i).getOperationId().toString()).equals(strList.get(j))){
                    wrapper.eq("role_id",roleId).eq("operation_id",strList.get(j)).set("status",1);
                    break;
                }else{
                    wrapper.eq("role_id",roleId).eq("operation_id",list.get(i).getOperationId()).set("status",0);
                }
            }
            roleOperationDao.update(null,wrapper);
        }
        return AjaxResult.success("设置成功");
    }

    @Override
    public AjaxResult getOperations() {

        List<Map<String,Object>> list = operationDao.selectMaps(null);
        return AjaxResult.success(list);
    }

    @Override
    public AjaxResult getRoleOperations(Long roleId) {

        // 特殊处理：检查是否为系统管理员角色
        QueryWrapper<Role> roleWrapper = new QueryWrapper<>();
        roleWrapper.eq("role_id", roleId);
        Role role = roleDao.selectOne(roleWrapper);

        List<Operation> list1 = new ArrayList<>();

        if (role != null && "系统管理员".equals(role.getRoleName())) {
            // 如果是系统管理员角色，返回所有权限点
            list1 = operationDao.selectList(null);
        } else {
            // 普通角色按正常逻辑查询权限
            QueryWrapper<RoleOperation> wrapper = new QueryWrapper<>();
            wrapper.eq("role_id",roleId).eq("status",1);
            List<RoleOperation> list = roleOperationDao.selectList(wrapper);

            for (int i = 0; i < list.size(); i++) {
                QueryWrapper<Operation> wrapper1 = new QueryWrapper<>();
                Long operationId = list.get(i).getOperationId();
                wrapper1.eq("operation_id",operationId);
                Operation operation = operationDao.selectOne(wrapper1);
                list1.add(operation);
            }
        }

        return AjaxResult.success(list1);
    }

    @Override
    public AjaxResult getRoles() {

        QueryWrapper<Role> wrapper = new QueryWrapper<>();
        wrapper.select("role_id","role_name").ne("role_id",11);
        List<Map<String, Object>> list = roleDao.selectMaps(wrapper);
        return AjaxResult.success(list);
    }
}
