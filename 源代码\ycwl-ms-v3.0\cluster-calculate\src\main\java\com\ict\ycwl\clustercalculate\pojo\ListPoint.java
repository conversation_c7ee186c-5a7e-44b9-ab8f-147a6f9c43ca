package com.ict.ycwl.clustercalculate.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListPoint {
    private String accumulation;
    private String name;
    private Double longitude;
    private Double latitude;
    private List<SpecialStore> specialStores;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpecialStore {
        private String storeName;
        private String specialType;
        private String remark;
        private Double longitude;
        private Double latitude;
    }
}
