package com.ict.ycwl.pickup.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pickup.pojo.request.pickupUser.PickupUserListRequest;
import com.ict.ycwl.pickup.pojo.request.pickupUser.PickupuserUpdateRequest;
import com.ict.ycwl.pickup.pojo.request.pickupUser.RecalculateRequest;
import com.ict.ycwl.pickup.pojo.vo.pickupUser.PickupUserVo;
import com.ict.ycwl.pickup.service.PickupUserService;
import com.ict.ycwl.pickup.utils.BeijingTimeUtil;
import com.ict.ycwl.pickup.utils.CsvImportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Api(tags = "取货户分析API")
@RestController
@RequestMapping("/pickupUser")
public class PickupUserController {
    @Autowired
    private PickupUserService pickupUserService;

    @Autowired
    private CsvImportUtil csvImportUtil;

    @Value("${file.DOWNLOAD_NULL_FROM_PATH}")
    private String DOWNLOAD_NULL_FROM_PATH;
    @ApiOperation("取货户分析列表")
    @GetMapping("/pickupUserList")
    public AjaxResult list(PickupUserListRequest pickupUserListRequest) {
        List<PickupUserVo> list = pickupUserService.getList(pickupUserListRequest);
        Integer pageNum = pickupUserListRequest.getPageNum();
        Integer pageSize = pickupUserListRequest.getPageSize();
        Page<PickupUserVo> page = pickupUserService.getPage(pageNum, pageSize, list);
        return AjaxResult.success("请求成功", page);
    }

    @ApiOperation("重新计算")
    @GetMapping("/pickupUserRecalculate")
    public AjaxResult reCalculate(RecalculateRequest recalculateRequest) {
        String info = pickupUserService.reCalculate(recalculateRequest);
        if("计算成功".equals(info)){
            return pickupUserService.recalculate();
        }
        return AjaxResult.error("计算失败");
    }

    @ApiOperation("导入取货户分析表格")
    @PostMapping("/pickupUserImport")
    public AjaxResult importExcel(@RequestParam("File") MultipartFile file, @RequestHeader("Authorization") String authorization) {
        String contentType = file.getContentType();
        File File = csvImportUtil.uploadFile(file);
        String beijingTime = "";
        String info = "文件格式不正确";
        try {
            beijingTime = BeijingTimeUtil.getBeijingTime();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            String regex = "^(\\d{4})/(0[1-9]|[1-9]|1[0-2])/([1-9]|0[1-9]|[12]\\d|3[01])$";
            // 编译正则表达式
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(beijingTime);
            //校验日期是否合法
            if (!StringUtil.isNotBlank(beijingTime) || !matcher.matches()) {
                // 设置时区为东八区
                ZoneId zoneId = ZoneId.of("Asia/Shanghai");

                // 获取东八区的当前时间
                ZonedDateTime now = ZonedDateTime.now(zoneId);

                // 设置时间格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

                // 格式化时间
                beijingTime = now.format(formatter);
            }
            //1.文件格式校验

            if("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType) || "application/vnd.ms-excel".equals(contentType)){
                boolean flag=pickupUserService.checkExportExcelFrom(File,contentType);
                System.out.println("文件校验是否通过："+flag);
                if(flag) {
                    info = pickupUserService.importExcel(File, authorization, beijingTime);
                }else{
                    info="文件格式错误,请上传符合格式的文件";
                }
            }else{
                info="文件类型错误,请上传指定的文件类型";
            }
        }
        return AjaxResult.success(info);
    }

    @ApiOperation("修改定点取货户")
    @PatchMapping("pickupUserUpdate")
    public AjaxResult updatePickupUser(PickupuserUpdateRequest updateRequest){
        int info =pickupUserService.myUpdateById(updateRequest);
        if(info==1){
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.error("修改失败");
    }

    @ApiOperation("导出取货户分析表格")
    @GetMapping("pickupUserExport")
    public AjaxResult Export(HttpServletResponse response) throws IOException {
        String info=pickupUserService.export(response);
        return AjaxResult.success(info);
    }

    @ApiOperation("设置为待分配")
    @PatchMapping("toBeAssigned")
    public AjaxResult toBeAssigned(@RequestParam ArrayList<Integer> ids){
        System.out.println(ids);
        boolean flag = pickupUserService.toBeAssigned(ids);
        if(flag){
            return AjaxResult.success("添加成功");
        }
        return AjaxResult.error("添加失败");
    }
    @ApiOperation("搜索下拉框")
    @GetMapping("searchDownBox")
    public AjaxResult searchDownBox(){
        AjaxResult obj=pickupUserService.getDownBox();
        return obj;
    }

    @ApiOperation("计算参数列表")
    @GetMapping("parameters")
    public AjaxResult getParameters(){
        AjaxResult info=pickupUserService.getParameters();
        return info;
    }
    @ApiOperation("修改参数列表")
    @GetMapping("updateParameters")
    public AjaxResult updateParameters(RecalculateRequest recalculateRequest){
        AjaxResult info=pickupUserService.updateParameters( recalculateRequest);
        return info;
    }

    //下载空白表格
    @ApiOperation("下载空白表格")
    @PostMapping("/downloadNullFrom")
    public void downloadNullFrom(@ApiParam(name = "code", value = "0表示取货户空白表格，1表示选址分析空白表格")
                                 @RequestParam int code,
                                 HttpServletResponse response) {
        //code:0商铺，1车辆.csvOrExcel:0标识csv文件，1标识xlsx文件,2标识xls文件
        System.out.println(1);
        String fileName = "";
        if (code == 0) {
                fileName = "取货地分析-导入表格示例.xlsx";
        } else {
                fileName = "选址分析-导入表格示例.xlsx";
        }
        String path = DOWNLOAD_NULL_FROM_PATH + fileName;
        try {
            // path是指想要下载的文件的路径
            File file = new File(path);
            // 获取文件名
            String filename = file.getName();
            // 获取文件后缀名
            String ext = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
            // 将文件写入输入流
            FileInputStream fileInputStream = new FileInputStream(file);
            InputStream fis = new BufferedInputStream(fileInputStream);
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            // 设置response的Header
            response.setCharacterEncoding("UTF-8");
            //Content-Disposition的作用：告知浏览器以何种方式显示响应返回的文件，用浏览器打开还是以附件的形式下载到本地保存
            //attachment表示以附件方式下载   inline表示在线打开   "Content-Disposition: inline; filename=文件名.mp3"
            // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
            // 告知浏览器文件的大小
            response.addHeader("Content-Length", "" + file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }
    @ApiOperation("地图标记")
    @PostMapping("/mapMarkers")
    public AjaxResult mapMarkers(){
       AjaxResult ajaxResult= pickupUserService.getMapMarkers();
       return ajaxResult;
    }

    @ApiOperation("获取取货户详情（包含路线信息）")
    @GetMapping("/detail/{id}")
    public AjaxResult getPickupUserDetail(@PathVariable Integer id) {
        PickupUserVo pickupUserVo = pickupUserService.getPickupUserWithRoute(id);
        if (pickupUserVo == null) {
            return AjaxResult.error("取货户不存在");
        }
        return AjaxResult.success(pickupUserVo);
    }

}
