CREATE DEFINER=`root`@`localhost` PROCEDURE `UpdateStoreAreaName`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE sid INT;
    DECLARE slongitude DECIMAL(9,6);
    DECLARE slatitude DECIMAL(9,6);
    DECLARE cid INT;
    DECLARE clongitude DECIMAL(9,6);
    DECLARE clatitude DECIMAL(9,6);
    DECLARE carea_name VARCHAR(255);
    DECLARE cur CURSOR FOR SELECT store_id, longitude, latitude FROM store WHERE area_name IS NULL OR area_name = '' AND store_address IS NOT NULL;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 创建一个临时表来存储最近的中心距离信息
    CREATE TEMPORARY TABLE IF NOT EXISTS nearest_center (
        store_id INT,
        center_id INT,
        distance DECIMAL(15,6)
    );

    -- 清空临时表
    TRUNCATE TABLE nearest_center;

    -- 打开游标
    OPEN cur;

    read_loop: LOOP
        FETCH cur INTO sid, slongitude, slatitude;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 初始化最近距离为一个很大的值
        SET @min_distance := 999999999.0;
        SET @nearest_center_id := NULL;

        -- 对于store表中的每条记录，找到最近的centerdistance记录
SELECT cenStore_id, lng, lat, cenStore_name
INTO @nearest_center_id, @clongitude, @clatitude, @nearest_center_area_name
FROM centerdistance
ORDER BY SQRT(POW(slongitude - lng, 2) + POW(slatitude - lat, 2))
LIMIT 1;

        -- 计算距离并更新最近的中心信息
        SET @distance := SQRT(POW(slongitude - clongitude, 2) + POW(slatitude - clatitude, 2));
        IF @distance < @min_distance THEN
            SET @min_distance := @distance;
            SET @nearest_center_id := cid;
            SET @nearest_center_area_name := carea_name;
        END IF;

        -- 将最近的中心信息插入到临时表
        INSERT INTO nearest_center (store_id, center_id, distance) VALUES (sid, @nearest_center_id, @min_distance);
    END LOOP;

    -- 关闭游标
    CLOSE cur;

    -- 更新store表中的area_name
    UPDATE store st
    JOIN nearest_center nc ON st.store_id = nc.store_id
    SET st.area_name = (SELECT cenStore_name FROM centerdistance WHERE cenStore_id = nc.center_id);

    -- 删除临时表
    DROP TEMPORARY TABLE IF EXISTS nearest_center;
END