一.聚集区算法：
1.遍历数据库，查看指定聚集区下的商铺
2.尝试不同的簇数，不同的簇数会生成不同的sse值
3.通过sse值，判断最佳的分簇数（bestk）（SSE曲线通常用于评估聚类算法的性能，sse数值越小代表聚类效果越好）
3.1计算sse值，计算一个簇中，累加所有非簇心点与簇心的（经度差的平方+纬度差的平方），即得到当前簇数下的sse值
4.有了bestk后，就能调用Kmeans算法，通过算法就能分簇。
5.利用分好的簇，得到每个簇的中心坐标，这个中心坐标并不是某个商铺的坐标，最后我们，遍历该簇下的点，找一个离中心坐标最近的点作为该簇的中心点
6.在数据库中，每次保存一个聚集区时，会自动生成该聚集区的id，然后我们通过聚集区中心点的坐标，中心点的坐标在数据库中也是该聚集区的坐标，将该聚集区下的所有商铺的聚集区id设置为聚集区坐标
7.数据库上聚集区显示中心点即是数据库中聚集区表中的一条数据，地图右侧是所有商铺的信息，点地图中心点，找到的是簇的中心点坐标，右侧中有不是中心点的商铺是找不到，地图中的点，但是点击后，移动到大致位置

算法二，获取sse集合及对应的簇数集合(cycleOfSSE)
	该段代码主要是从设定的簇数范围中获取不同簇数对应的sse值和对应的簇数，这里的sse集合每一项是一个sseValue值

算法三，kMeans算法：
	K-means算法是一种迭代求解的聚类分析算法，其核心思想是将数据集中的n个对象划分为K个聚类，使得每个对象到其所属聚类的中心（或称为均值点、质心）的距离之和最小。这里所说的距离指的是欧氏距离，这里的欧氏距离是指二维坐标系中两点之间的距离

算法四，计算簇中每个点和簇心得经纬度之积的和(calculateSSE)
	这里的和指的是sseValue：从一个包含很多个簇的集合中，遍历每一个簇，计算该簇中的点到中心点的距离，累加到sseValue中。

算法五，获取最佳分簇数(getBestK)
	根据sse集合和记录分簇数的集合，用sse集合中的前一项减后一项，产生差值最大的那一组数据的被减数对应的索引，返回分簇数集合该索引对应的值就是最佳分簇数。

算法六，根据 storeNumber 划分簇 (splitClusterResultEctry)
	对分好的簇的商铺数量进行判断，如果小于storeNumber的话就是符合要求的簇，将该簇添加到finalList集合中，并将该簇的簇心保存到clusterCenters中，如果大于storeNumebr的就添加到kmeansTempList中，

算法七，检查簇中是否所有商铺为同一个商铺的情况 (isExistRepeat)
	遍历簇集合，对每个簇进行检查，如果存在簇中所有的商铺都是同一个商铺的情况就返回该簇的索引，如果遍历完整个集合都不存在这样一个簇就返回-1，表示没有找到。
	
	
