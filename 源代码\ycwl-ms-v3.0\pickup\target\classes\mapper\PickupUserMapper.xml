<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pickup.mapper.PickupUserMapper">

    <insert id="copyByStore" >
        INSERT INTO pickup_user (customer_code, contact_name, store_name, customer_manager_name,
                                 contact_phone, store_address, road_grade, gear, longitude, latitude, accumulation_id)
        SELECT customer_code,
               contact_name,
               store_name,
               customer_manager_name,
               contact_phone,
               store_address,
               location_type,
               gear,
               longitude,
               latitude,
               accumulation_id
        FROM store
        WHERE is_delete = 0 ON DUPLICATE KEY
        UPDATE
            accumulation_id =
        VALUES (accumulation_id);
    </insert>

    <select id="calculateDeliveryDistance" resultType="int">
        CALL CalculateDeliveryDistance();
    </select>

    <select id="checkCustomerCode" resultType="int">
        select COUNT(*) from pickup_user where customer_code=#{customerCode}
    </select>

    <update id="updatePickupTypeByCustomerCode">
        update pickup_user set type=#{pickupType} where customer_code=#{customerCode}
    </update>

    <update id="updateByList">
        <foreach collection="pickupUsers" item="item" index="index" open="" close="" separator=";">
            update pickup_user
            <set>
                weights = #{item.weights},
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <select id="selectDownBox" resultType="java.lang.String">
        SELECT DISTINCT gear
        FROM pickup_user where gear is NOT NULL;
    </select>

    <update id="updateStatusByid">
        update pickup_user set color=5 where id=#{id}
    </update>

    <select id="selectAccByAccId" resultType="com.ict.ycwl.pickup.pojo.dto.CheckInPointName">
        select accumulation_name,accumulation_address from accumulation where accumulation_id=#{accumulationId1}
    </select>

    <select id="MyselectList" resultType="com.ict.ycwl.pickup.pojo.dto.PickupUserDto">
        SELECT id,customer_code,contact_name,store_name,customer_manager_name,contact_phone,store_address,road_grade,gear,delivery_distance,pickup_containers,`type`,weights,locks,color,p.longitude,p.latitude,p.accumulation_id,
        CONCAT(acc.accumulation_name, '-', acc.accumulation_address) as accumulationName
        FROM pickup_user p
        LEFT JOIN
        accumulation acc ON acc.accumulation_id = p.accumulation_id
        <where>
            <if test="storeName != null and storeName != ''">
                AND store_name LIKE CONCAT('%', #{storeName}, '%')
            </if>
            <if test="customerCode != null and customerCode != ''">
                AND customer_code = #{customerCode}
            </if>
            <if test="storeAddress != null and storeAddress != ''">
                AND store_address LIKE CONCAT('%', #{storeAddress}, '%')
            </if>
            <if test="gear != null and gear != ''">
                AND gear = #{gear}
            </if>
            <if test="deliveryDistance != null and deliveryDistance > 0">
                AND delivery_distance = #{deliveryDistance}
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="pickupContainers != null and pickupContainers != ''">
                AND pickup_containers LIKE CONCAT('%', #{pickupContainers}, '%')
            </if>
            <if test="color != null and color != 0">
                AND color = #{color}
            </if>
            <if test="accumulationId != null and accumulationId != 0">
                AND p.accumulation_id = #{accumulationId}
            </if>
        </where>
    </select>

    <select id="selectAccIdByAccName" resultType="Long">
        select accumulation_id from accumulation where accumulation_address=#{accumulationName} and is_delete=0 limit 1
    </select>
</mapper>