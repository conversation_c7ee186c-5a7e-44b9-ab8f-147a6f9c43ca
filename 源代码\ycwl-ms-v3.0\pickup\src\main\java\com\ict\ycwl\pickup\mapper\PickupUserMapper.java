package com.ict.ycwl.pickup.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.pickup.pojo.dto.CheckInPointName;
import com.ict.ycwl.pickup.pojo.dto.PickupUserDto;
import com.ict.ycwl.pickup.pojo.entity.PickupUser;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PickupUserMapper extends BaseMapper<PickupUser> {
    int copyByStore();

    int calculateDeliveryDistance();

    int checkCustomerCode(String customerCode);

    int updatePickupTypeByCustomerCode(String customerCode, String pickupType);

    int updateByList(List<PickupUser> pickupUsers);

    List<String> selectDownBox();

    int updateStatusByid(int id);

    CheckInPointName selectAccByAccId(Long accumulationId1);

    List<PickupUserDto> MyselectList(String storeName, String customerCode, String storeAddress, String gear, Double deliveryDistance, String type, String pickupContainers, int color, long accumulationId);

    Long selectAccIdByAccName(String accumulationName);

    String getRouteNameByCustomerCode(String customerCode);
}
