# 创建挂载目录
mkdir -p /usr/local/nginx/conf
mkdir -p /usr/local/nginx/log
mkdir -p /usr/local/nginx/html

# 初次生成容器
docker run --name nginx -p 9090:80 -d nginx:1.22.1
# 将容器nginx.conf文件复制到宿主机
docker cp nginx:/etc/nginx/nginx.conf /usr/local/nginx/conf/nginx.conf
# 将容器conf.d文件夹下内容复制到宿主机
docker cp nginx:/etc/nginx/conf.d /usr/local/nginx/conf/conf.d
# 将容器中的html文件夹复制到宿主机
docker cp nginx:/usr/share/nginx/html /usr/local/nginx

# 直接执行docker rm nginx或者以容器id方式关闭容器
# 找到nginx对应的容器id
docker ps -a
# 关闭该容器
docker stop nginx
# 删除该容器
docker rm nginx

# 再次创建容器
docker run -p 9091:80 --name nginx -v /usr/local/nginx/conf/nginx.conf:/etc/nginx/nginx.conf -v /usr/local/nginx/conf/conf.d:/etc/nginx/conf.d -v /usr/local/nginx/log:/var/log/nginx -v /usr/local/nginx/html:/usr/share/nginx/html -d nginx:1.22.1



540前端部署：
将dist文件夹下文件放在：根目录>www>wwwroot >ycwl >
即可

部署到烟草服务器上：
解压前端给的dist压缩包，解压出 assets文件夹 和 index.html 。
将 assets文件夹和index.html 直接拖到 烟草服务器的/usr/local/nginx/html下，即可。