package com.ict.ycwl.clustercalculate.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ict.ycwl.clustercalculate.mapper.AccumulationMapper;
import com.ict.ycwl.clustercalculate.mapper.ErrorPointMapper;
import com.ict.ycwl.clustercalculate.mapper.RouteMapper;
import com.ict.ycwl.clustercalculate.mapper.StoreMapper;
import com.ict.ycwl.clustercalculate.pojo.*;
import com.ict.ycwl.clustercalculate.pojo.vo.PerviewPoint;
import com.ict.ycwl.clustercalculate.pojo.vo.PerviewPointVo;
import com.ict.ycwl.clustercalculate.service.PointService;
import com.ict.ycwl.common.web.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.Math.toRadians;

/**
 * <AUTHOR>
 */
@Service
public class PointServiceImpl implements PointService {

    @Autowired
    private AccumulationMapper accumulationMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private ErrorPointMapper errorPointMapper;

    @Autowired
    private RouteMapper routeMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;


    @Override
    public Object getMapResultPoints() {
        MapResult result = new MapResult();
        List<MapResultPoint> resultPoints = new ArrayList<>();
        //获得聚集区点
        QueryWrapper<Accumulation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper);
        //获得错误点，用于为下面循环做判断
        QueryWrapper<ErrorPoint> queryWrapper1 = new QueryWrapper<>();
//        queryWrapper1.eq("is_delete", 0).eq("is_exist", 1);
        queryWrapper1.eq("is_delete", 0);
        List<ErrorPoint> errorPoints = errorPointMapper.selectList(queryWrapper1);

        //商铺的集合，其中LngAndLat代表当前聚集区的其中一个商铺，ArrayList<LngAndLat>表示一个聚集区
        ArrayList<ArrayList<LngAndLat>> accumulationStoreList = new ArrayList<>();
        //聚集区的集合
        ArrayList<LngAndLat> accumulationList = new ArrayList<>();

        for (Accumulation accumulation : accumulations) {
            accumulationList.add(new LngAndLat(accumulation.getLongitude(), accumulation.getLatitude()));

            //添加该聚集区所包含的商铺
            ArrayList<LngAndLat> list = new ArrayList<>();
            QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
            storeQueryWrapper.eq("accumulation_id", accumulation.getAccumulationId()).eq("is_delete", 0);
            List<Store> stores = storeMapper.selectList(storeQueryWrapper);
            for (Store s : stores) {
                list.add(new LngAndLat(s.getLongitude(), s.getLatitude()));
            }
            accumulationStoreList.add(list);
        }

        // 注释掉错误点的添加逻辑，只显示打卡点
        // 错误点信息将通过打卡点的关联商铺信息来展示
        /*
        //添加错误点
        for (ErrorPoint e : errorPoints) {
            LngAndLat l1 = new LngAndLat(e.getCurrentStoreLongitude(), e.getCurrentStoreLatitude());
            LngAndLat l2 = new LngAndLat(e.getPairingStoreLongitude(), e.getPairingStoreLatitude());
            for (ArrayList<LngAndLat> arrayList : accumulationStoreList) {
                if (arrayList.contains(l1) && arrayList.contains(l2)) {
                    MapResultPoint mapResultPoint = new MapResultPoint();
                    QueryWrapper<Store> queryWrapper2 = new QueryWrapper<>();
                    queryWrapper2.eq("is_delete", 0).eq("longitude", e.getCurrentStoreLongitude()).eq("latitude", e.getCurrentStoreLatitude());
                    Store store = null;
                    try {
                        store = storeMapper.selectList(queryWrapper2).get(0);
                    } catch (IndexOutOfBoundsException i) {
                        throw new IllegalAccessError("商铺可能已被删除");
                    }
                    addRemainderErrorPoints(resultPoints, mapResultPoint, store);

                    MapResultPoint mapResultPoint1 = new MapResultPoint();
                    QueryWrapper<Store> queryWrapper3 = new QueryWrapper<>();
                    queryWrapper3.eq("is_delete", 0).eq("longitude", e.getPairingStoreLongitude()).eq("latitude", e.getPairingStoreLatitude());
                    Store store1 = null;
                    try {
                        store1 = storeMapper.selectList(queryWrapper3).get(0);
                    } catch (IndexOutOfBoundsException i) {
                        throw new IllegalAccessError("商铺可能已被删除");
                    }
                    addRemainderErrorPoints(resultPoints, mapResultPoint1, store1);

                    //如果accumulationList中存在这两个点，则删除这两个点，因为虽然点有可能是聚集区，这两个点已经被标记为了error
                    accumulationList.remove(l1);
                    accumulationList.remove(l2);
                }
            }
        }
        */

        int i = 1;
        //将过滤后的聚集区集合加到result里
        for (LngAndLat lngAndLat : accumulationList) {
            MapResultPoint mapResultPoint = new MapResultPoint();
            QueryWrapper<Accumulation> accumulationQueryWrapper = new QueryWrapper<>();
            accumulationQueryWrapper.eq("is_delete", 0).eq("longitude", lngAndLat.getLongitude()).eq("latitude", lngAndLat.getLatitude());
            Accumulation accumulation = accumulationMapper.selectOne(accumulationQueryWrapper);

            ArrayList<Double> arrayList = new ArrayList<Double>();
            arrayList.add(lngAndLat.getLongitude());
            arrayList.add(lngAndLat.getLatitude());
            mapResultPoint.setLnglat(arrayList);
            mapResultPoint.setName(accumulation.getAccumulationAddress());
            mapResultPoint.setStyle(i++);
            mapResultPoint.setAccumulationId(accumulation.getAccumulationId());
            mapResultPoint.setRouteId(accumulation.getRouteId());
            mapResultPoint.setAreaId(accumulation.getAreaId());
            mapResultPoint.setState("center");
            mapResultPoint.setAccumulation(accumulation.getAccumulationName());

            // 查询该打卡点关联的特殊商铺（is_special = 1 的商铺）
            QueryWrapper<Store> specialStoreWrapper = new QueryWrapper<>();
            specialStoreWrapper.eq("accumulation_id", accumulation.getAccumulationId())
                    .eq("is_delete", 0)
                    .eq("is_special", 1);
            List<Store> specialStores = storeMapper.selectList(specialStoreWrapper);

            // 转换为SpecialStore对象列表
            List<MapResultPoint.SpecialStore> specialStoreList = new ArrayList<>();
            for (Store store : specialStores) {
                MapResultPoint.SpecialStore specialStore = new MapResultPoint.SpecialStore();
                // 使用商铺地址作为显示名称，如果地址为空则使用商铺名称
                String displayName = (store.getStoreAddress() != null && !store.getStoreAddress().trim().isEmpty())
                    ? store.getStoreAddress() : store.getStoreName();
                specialStore.setStoreName(displayName);
                specialStore.setSpecialType(store.getSpecialType());
                specialStore.setRemark(store.getRemark());
                specialStore.setLongitude(store.getLongitude());
                specialStore.setLatitude(store.getLatitude());
                specialStoreList.add(specialStore);
            }
            mapResultPoint.setSpecialStores(specialStoreList);

            resultPoints.add(mapResultPoint);
        }

        result.setPoint(resultPoints);
        return result;
    }

    private void addRemainderErrorPoints(List<MapResultPoint> resultPoints, MapResultPoint mapResultPoint, Store store) {
        ArrayList<Double> arrayList = new ArrayList<Double>();
        arrayList.add(store.getLongitude());
        arrayList.add(store.getLatitude());
        mapResultPoint.setLnglat(arrayList);
        mapResultPoint.setName(store.getStoreAddress());
        mapResultPoint.setAccumulationId(store.getAccumulationId());
        mapResultPoint.setRouteId(store.getRouteId());
        mapResultPoint.setAreaId(store.getAreaId());
        mapResultPoint.setState("error");
        mapResultPoint.setRemark(store.getRemark());
        mapResultPoint.setSpecialType(store.getSpecialType());
        QueryWrapper<Accumulation> accumulationQueryWrapper = new QueryWrapper<>();
        accumulationQueryWrapper.eq("is_delete", 0).eq("accumulation_id", store.getAccumulationId());
        Accumulation accumulation = accumulationMapper.selectOne(accumulationQueryWrapper);
        mapResultPoint.setAccumulation(accumulation.getAccumulationName());
        if (!resultPoints.contains(mapResultPoint)) {
            resultPoints.add(mapResultPoint);
        }
    }

    @Override
    public List<ListPoint> getListResultPoints() {
        List<ListPoint> listPoints = new ArrayList<>();

        //查询所有未被删除的聚集区
        QueryWrapper<Accumulation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper);
        for (Accumulation accumulation : accumulations) {
            List<Store> stores = storeMapper.selectList(new QueryWrapper<Store>().eq("accumulation_id", accumulation.getAccumulationId()));
            for (Store s : stores) {
                ListPoint listPoint = new ListPoint();
                listPoint.setName(s.getStoreAddress());
                listPoint.setAccumulation(accumulation.getAccumulationName());
                listPoint.setLongitude(s.getLongitude());
                listPoint.setLatitude(s.getLatitude());
                listPoints.add(listPoint);
            }
        }
        return listPoints;
    }


   /* @Override
    public int checkErrorPoints() {
        List<MapResultPoint> resultPoints = new ArrayList<>();
        //获得聚集区点
        QueryWrapper<Accumulation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper);
        //获得错误点，用于为下面循环做判断
        QueryWrapper<ErrorPoint> queryWrapper1 = new QueryWrapper<>();
//        queryWrapper1.eq("is_delete", 0).eq("is_exist", 1);
        queryWrapper1.eq("is_delete", 0);
        List<ErrorPoint> errorPoints = errorPointMapper.selectList(queryWrapper1);

        //商铺的集合，其中LngAndLat代表当前聚集区的其中一个商铺，ArrayList<LngAndLat>表示一个聚集区
        ArrayList<ArrayList<LngAndLat>> accumulationStoreList = new ArrayList<>();

        for (Accumulation accumulation : accumulations) {
            //添加该聚集区所包含的商铺
            ArrayList<LngAndLat> list = new ArrayList<>();
            QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
            storeQueryWrapper.eq("accumulation_id", accumulation.getAccumulationId()).eq("is_delete", 0);
            List<Store> stores = storeMapper.selectList(storeQueryWrapper);
            for (Store s : stores) {
                list.add(new LngAndLat(s.getLongitude(), s.getLatitude()));
            }
            accumulationStoreList.add(list);
        }

        //添加错误点
        for (ErrorPoint e : errorPoints) {
            LngAndLat l1 = new LngAndLat(e.getCurrentStoreLongitude(), e.getCurrentStoreLatitude());
            LngAndLat l2 = new LngAndLat(e.getPairingStoreLongitude(), e.getPairingStoreLatitude());
            for (ArrayList<LngAndLat> arrayList : accumulationStoreList) {
                if (arrayList.contains(l1) && arrayList.contains(l2)) {
                    MapResultPoint mapResultPoint = new MapResultPoint();
                    QueryWrapper<Store> queryWrapper2 = new QueryWrapper<>();
                    queryWrapper2.eq("is_delete", 0).eq("longitude", e.getCurrentStoreLongitude()).eq("latitude", e.getCurrentStoreLatitude());
                    Store store = null;
                    try {
                        store = storeMapper.selectList(queryWrapper2).get(0);
                    } catch (IndexOutOfBoundsException i) {
                        throw new IllegalAccessError("商铺可能已被删除");
                    }
                    addRemainderErrorPoints(resultPoints, mapResultPoint, store);

                    MapResultPoint mapResultPoint1 = new MapResultPoint();
                    QueryWrapper<Store> queryWrapper3 = new QueryWrapper<>();
                    queryWrapper3.eq("is_delete", 0).eq("longitude", e.getPairingStoreLongitude()).eq("latitude", e.getPairingStoreLatitude());
                    Store store1 = null;
                    try {
                        store1 = storeMapper.selectList(queryWrapper3).get(0);
                    } catch (IndexOutOfBoundsException i) {
                        throw new IllegalAccessError("商铺可能已被删除");
                    }
                    addRemainderErrorPoints(resultPoints, mapResultPoint1, store1);
                }
            }
        }

        return resultPoints.size();
    }
*/
    @Override
    public int checkErrorPoints() {
        // 批量预加载所有必要数据
        List<Accumulation> accumulations = getActiveAccumulations();
        List<ErrorPoint> errorPoints = getActiveErrorPoints();
        Map<String, Store> storeMap = buildStoreCoordinateMap();
        Map<Long, Set<String>> accumulationStoreMap = buildAccumulationStoreMap(accumulations, storeMap);

        List<MapResultPoint> resultPoints = new ArrayList<>();

        // 遍历错误点进行快速匹配
        for (ErrorPoint e : errorPoints) {
            String key1 = generateCoordinateKey(e.getCurrentStoreLongitude(), e.getCurrentStoreLatitude());
            String key2 = generateCoordinateKey(e.getPairingStoreLongitude(), e.getPairingStoreLatitude());

            Store store1 = storeMap.get(key1);
            Store store2 = storeMap.get(key2);

            if (store1 == null || store2 == null) {
                throw new IllegalAccessError("商铺可能已被删除");
            }

            // 检查两点是否属于同一聚集区
            if (belongToSameAccumulation(store1, store2, accumulationStoreMap)) {
                addPointToResult(resultPoints, store1);
                addPointToResult(resultPoints, store2);
            }
        }

        return resultPoints.size();
    }

    // --- 辅助方法 ---
    private List<Accumulation> getActiveAccumulations() {
        QueryWrapper<Accumulation> wrapper = new QueryWrapper<>();
        wrapper.eq("is_delete", 0);
        return accumulationMapper.selectList(wrapper);
    }

    private List<ErrorPoint> getActiveErrorPoints() {
        QueryWrapper<ErrorPoint> wrapper = new QueryWrapper<>();
        wrapper.eq("is_delete", 0);
        return errorPointMapper.selectList(wrapper);
    }

    private Map<String, Store> buildStoreCoordinateMap() {
        QueryWrapper<Store> wrapper = new QueryWrapper<>();
        wrapper.eq("is_delete", 0);

        // 使用toMap的重载方法处理重复键
        return storeMapper.selectList(wrapper)
                .stream()
                .collect(Collectors.toMap(
                        s -> generateCoordinateKey(s.getLongitude(), s.getLatitude()),
                        s -> s,
                        (existing, replacement) -> existing  // 当重复时保留已有值
                ));
    }

    private Map<Long, Set<String>> buildAccumulationStoreMap(List<Accumulation> accumulations, Map<String, Store> storeMap) {
        Map<Long, Set<String>> resultMap = new HashMap<>();

        for (Accumulation acc : accumulations) {
            Set<String> coordinateSet = storeMap.values().stream()
                    .filter(store -> acc.getAccumulationId().equals(store.getAccumulationId()))
                    .map(store -> generateCoordinateKey(store.getLongitude(), store.getLatitude()))
                    .collect(Collectors.toSet());

            resultMap.put(acc.getAccumulationId(), coordinateSet);
        }

        return resultMap;
    }

    private boolean belongToSameAccumulation(Store store1, Store store2, Map<Long, Set<String>> accumulationStoreMap) {
        Long accId1 = store1.getAccumulationId();
        Long accId2 = store2.getAccumulationId();

        // 只要两个商铺都有聚集区分配即可，不要求必须在同一个聚集区
        if (accId1 == null || accId2 == null) {
            return false;
        }

        // 检查两个商铺是否都在聚集区映射中
        Set<String> storeSet1 = accumulationStoreMap.get(accId1);
        Set<String> storeSet2 = accumulationStoreMap.get(accId2);
        String key1 = generateCoordinateKey(store1.getLongitude(), store1.getLatitude());
        String key2 = generateCoordinateKey(store2.getLongitude(), store2.getLatitude());

        return storeSet1 != null && storeSet1.contains(key1) &&
               storeSet2 != null && storeSet2.contains(key2);
    }

    private void addPointToResult(List<MapResultPoint> resultPoints, Store store) {
        MapResultPoint point = new MapResultPoint();
        // 设置必要属性（根据实际实现补充）
        resultPoints.add(point);
    }

    private String generateCoordinateKey(double longitude, double latitude) {
        return longitude + "|" + latitude;
    }


    @Override
    public List<ListErrorCluster> getErrorPoints1() {
        //获得聚集区点
        QueryWrapper<Accumulation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper);
        //获得错误点，用于为下面循环做判断
        QueryWrapper<ErrorPoint> queryWrapper1 = new QueryWrapper<>();
//        queryWrapper1.eq("is_delete", 0).eq("is_exist", 1);
        queryWrapper1.eq("is_delete", 0);
        List<ErrorPoint> errorPointsList = errorPointMapper.selectList(queryWrapper1);

        //商铺的集合，其中LngAndLat代表当前聚集区的其中一个商铺，ArrayList<LngAndLat>表示一个聚集区
        ArrayList<ArrayList<LngAndLat>> accumulationStoreList = new ArrayList<>();

        for (Accumulation accumulation : accumulations) {
            //添加该聚集区所包含的商铺
            ArrayList<LngAndLat> list = new ArrayList<>();
            QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
            storeQueryWrapper.eq("accumulation_id", accumulation.getAccumulationId()).eq("is_delete", 0);
            List<Store> stores = storeMapper.selectList(storeQueryWrapper);
            for (Store s : stores) {
                list.add(new LngAndLat(s.getLongitude(), s.getLatitude()));
            }
            accumulationStoreList.add(list);
        }

        //创建错误点数组
        List<ErrorPoint> errorPoints = new ArrayList<>();
        //添加错误点
        for (ErrorPoint e : errorPointsList) {
            LngAndLat l1 = new LngAndLat(e.getCurrentStoreLongitude(), e.getCurrentStoreLatitude());
            LngAndLat l2 = new LngAndLat(e.getPairingStoreLongitude(), e.getPairingStoreLatitude());
            for (ArrayList<LngAndLat> arrayList : accumulationStoreList) {
                if (arrayList.contains(l1) && arrayList.contains(l2)) {
                    errorPoints.add(e);
                }
            }
        }

        //根据错误点获取所有含有错误点的聚集区名字
        List<String> accumulationName = new ArrayList<>();
        for (ErrorPoint errorPoint : errorPoints) {
            QueryWrapper<Store> s = new QueryWrapper<>();
            s.eq("longitude", errorPoint.getCurrentStoreLongitude()).eq("latitude", errorPoint.getCurrentStoreLatitude()).eq("is_delete", 0);
            Store store = storeMapper.selectList(s).get(0);

            QueryWrapper<Accumulation> q = new QueryWrapper<>();
            q.eq("accumulation_id", store.getAccumulationId());
            Accumulation accumulation = accumulationMapper.selectOne(q);

            if (!accumulationName.contains(accumulation.getAccumulationName())) {
                accumulationName.add(accumulation.getAccumulationName());
            }
        }

        List<ListErrorCluster> listErrorClusters = new ArrayList<>();
        //将错误点装到对应的聚集区
        for (String aName : accumulationName) {
            HashMap<LngAndLat, ArrayList<LngAndLat>> map = new HashMap<>();
            for (ErrorPoint errorPoint : errorPoints) {
                QueryWrapper<Store> s = new QueryWrapper<>();
                s.eq("longitude", errorPoint.getCurrentStoreLongitude()).eq("latitude", errorPoint.getCurrentStoreLatitude()).eq("is_delete", 0);
                Store store = storeMapper.selectList(s).get(0);

                QueryWrapper<Accumulation> q = new QueryWrapper<>();
                q.eq("accumulation_id", store.getAccumulationId());
                Accumulation accumulation = accumulationMapper.selectOne(q);
                //判断当前遍历的errorPoint元素的起始坐标点的所属聚集区，是否等于当前遍历的aName
                if (accumulation.getAccumulationName().equals(aName)) {
                    LngAndLat key = new LngAndLat(store.getLongitude(), store.getLatitude());
                    LngAndLat value = new LngAndLat(errorPoint.getPairingStoreLongitude(), errorPoint.getPairingStoreLatitude());
                    if (map.containsKey(key)) {
                        map.get(key).add(value);
                    } else {
                        ArrayList<LngAndLat> list = new ArrayList<>();
                        list.add(value);
                        map.put(key, list);
                    }
                }
            }

            ListErrorCluster listErrorCluster = new ListErrorCluster();
            List<ListErrorPointFather> data = new ArrayList<>();
            Set<Map.Entry<LngAndLat, ArrayList<LngAndLat>>> entries = map.entrySet();

            //一个entry为一个ListErrorPointFather
            for (Map.Entry<LngAndLat, ArrayList<LngAndLat>> entry : entries) {
                //包含有错误点的聚集区
                LngAndLat key = entry.getKey();

                QueryWrapper<Store> s = new QueryWrapper<>();
                s.eq("longitude", key.getLongitude()).eq("latitude", key.getLatitude()).eq("is_delete", 0);
                Store store = storeMapper.selectList(s).get(0);

                ListErrorPointFather father = new ListErrorPointFather();
                father.setLongitude(key.getLongitude());
                father.setLatitude(key.getLatitude());
                father.setName(store.getStoreAddress());
                father.setSon(new ArrayList<>());

                ArrayList<LngAndLat> value = entry.getValue();
                for (LngAndLat l : value) {
                    QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
                    storeQueryWrapper.eq("longitude", l.getLongitude()).eq("latitude", l.getLatitude()).eq("is_delete", 0);
                    Store store1 = storeMapper.selectList(storeQueryWrapper).get(0);
                    father.getSon().add(new ListErrorPointSon(store1.getLongitude(), store1.getLatitude(), store1.getStoreAddress()));
                }

                listErrorCluster.setNumber(listErrorCluster.getNumber() + value.size());

                data.add(father);
            }

            listErrorCluster.setData(data);
            listErrorCluster.setAccumulationName(aName);

            listErrorClusters.add(listErrorCluster);
        }

        return listErrorClusters;
    }
    /*@Override
    public List<ListErrorPointFather> getErrorPointsPlus() {
        // 获得聚集区点
        QueryWrapper<Accumulation> accumulationWrapper = new QueryWrapper<>();
        accumulationWrapper.eq("is_delete", 0);
        List<Accumulation> accumulations = accumulationMapper.selectList(accumulationWrapper);

        // 获得错误点
        QueryWrapper<ErrorPoint> errorPointWrapper = new QueryWrapper<>();
        errorPointWrapper.eq("is_delete", 0);
        List<ErrorPoint> errorPointsList = errorPointMapper.selectList(errorPointWrapper);

        // 存储聚集区及其包含的商铺经纬度
        Map<Long, List<LngAndLat>> accumulationStoreMap = new HashMap<>();
        for (Accumulation accumulation : accumulations) {
            List<LngAndLat> storePoints = new ArrayList<>();
            QueryWrapper<Store> storeWrapper = new QueryWrapper<>();
            storeWrapper.eq("accumulation_id", accumulation.getAccumulationId())
                    .eq("is_delete", 0);
            List<Store> stores = storeMapper.selectList(storeWrapper);
            for (Store store : stores) {
                storePoints.add(new LngAndLat(store.getLongitude(), store.getLatitude()));
            }
            accumulationStoreMap.put(accumulation.getAccumulationId(), storePoints);
        }

        // 筛选出属于同一聚集区的错误点
        List<ErrorPoint> validErrorPoints = new ArrayList<>();
        for (ErrorPoint errorPoint : errorPointsList) {
            LngAndLat currentPoint = new LngAndLat(errorPoint.getCurrentStoreLongitude(), errorPoint.getCurrentStoreLatitude());
            LngAndLat pairingPoint = new LngAndLat(errorPoint.getPairingStoreLongitude(), errorPoint.getPairingStoreLatitude());

            for (Map.Entry<Long, List<LngAndLat>> entry : accumulationStoreMap.entrySet()) {
                List<LngAndLat> storePoints = entry.getValue();
                if (storePoints.contains(currentPoint) && storePoints.contains(pairingPoint)) {
                    validErrorPoints.add(errorPoint);
                    break;
                }
            }
        }

        // 构建错误点映射：起始点 -> 配对点列表
        Map<LngAndLat, List<LngAndLat>> errorPointMap = new HashMap<>();
        for (ErrorPoint errorPoint : validErrorPoints) {
            LngAndLat startPoint = new LngAndLat(
                    errorPoint.getCurrentStoreLongitude(),
                    errorPoint.getCurrentStoreLatitude()
            );

            LngAndLat endPoint = new LngAndLat(
                    errorPoint.getPairingStoreLongitude(),
                    errorPoint.getPairingStoreLatitude()
            );

            errorPointMap.computeIfAbsent(startPoint, k -> new ArrayList<>()).add(endPoint);
        }

        // 构建返回结果
        List<ListErrorPointFather> result = new ArrayList<>();
        for (Map.Entry<LngAndLat, List<LngAndLat>> entry : errorPointMap.entrySet()) {
            LngAndLat startPoint = entry.getKey();
            List<LngAndLat> endPoints = entry.getValue();

            // 获取起始点对应的店铺信息
            QueryWrapper<Store> startStoreWrapper = new QueryWrapper<>();
            startStoreWrapper.eq("longitude", startPoint.getLongitude())
                    .eq("latitude", startPoint.getLatitude())
                    .eq("is_delete", 0);
            Store startStore = storeMapper.selectOne(startStoreWrapper);

            if (startStore == null) continue;

            // 创建父节点
            ListErrorPointFather father = new ListErrorPointFather();
            father.setLongitude(startPoint.getLongitude());
            father.setLatitude(startPoint.getLatitude());
            father.setName(startStore.getStoreAddress());
            father.setSon(new ArrayList<>());

            // 添加所有子节点（配对点）
            for (LngAndLat endPoint : endPoints) {
                // 获取配对点对应的店铺信息
                QueryWrapper<Store> endStoreWrapper = new QueryWrapper<>();
                endStoreWrapper.eq("longitude", endPoint.getLongitude())
                        .eq("latitude", endPoint.getLatitude())
                        .eq("is_delete", 0);
                Store endStore = storeMapper.selectOne(endStoreWrapper);

                if (endStore != null) {
                    father.getSon().add(new ListErrorPointSon(
                            endPoint.getLongitude(),
                            endPoint.getLatitude(),
                            endStore.getStoreAddress(),
                            endStore.getSpecialType(),
                            endStore.getRemark()
                    ));
                }
            }

            result.add(father);
        }
        return result;
    }
*/
    @Override
    public List<ListErrorPointFather> getErrorPointsPlus() {
        // 1. 批量查询聚集区、错误点、店铺数据
        List<Accumulation> accumulations = getAccumulations();
        List<ErrorPoint> errorPointsList = getErrorPoints();
        Map<Long, List<LngAndLat>> accumulationStoreMap = buildAccumulationStoreMap(accumulations);

        // 2. 构建店铺到聚集区的映射 (优化点1)
        Map<LngAndLat, Long> pointToAccumulationMap = new HashMap<>();
        accumulationStoreMap.forEach((accId, points) ->
                points.forEach(point -> pointToAccumulationMap.put(point, accId))
        );

        // 3. 快速筛选有效错误点 (优化点2)
        List<ErrorPoint> validErrorPoints = errorPointsList.stream()
                .filter(ep -> {
                    LngAndLat current = new LngAndLat(ep.getCurrentStoreLongitude(), ep.getCurrentStoreLatitude());
                    LngAndLat pairing = new LngAndLat(ep.getPairingStoreLongitude(), ep.getPairingStoreLatitude());

                    Long currAccId = pointToAccumulationMap.get(current);
                    Long pairAccId = pointToAccumulationMap.get(pairing);

                    // 只要两个商铺都存在于某个聚集区中即可，不要求必须在同一个聚集区
                    return currAccId != null && pairAccId != null;
                })
                .collect(Collectors.toList());

        // 4. 预加载所有店铺数据 (优化点3)
        Set<LngAndLat> allPoints = validErrorPoints.stream()
                .flatMap(ep -> Stream.of(
                        new LngAndLat(ep.getCurrentStoreLongitude(), ep.getCurrentStoreLatitude()),
                        new LngAndLat(ep.getPairingStoreLongitude(), ep.getPairingStoreLatitude())
                ))
                .collect(Collectors.toSet());

        Map<LngAndLat, Store> storesMap = getStoresByPoints(allPoints);

        // 5. 构建错误点映射
        Map<LngAndLat, List<LngAndLat>> errorPointMap = new HashMap<>();
        for (ErrorPoint ep : validErrorPoints) {
            LngAndLat start = new LngAndLat(ep.getCurrentStoreLongitude(), ep.getCurrentStoreLatitude());
            LngAndLat end = new LngAndLat(ep.getPairingStoreLongitude(), ep.getPairingStoreLatitude());
            errorPointMap.computeIfAbsent(start, k -> new ArrayList<>()).add(end);
        }

        // 6. 构建结果集
        return errorPointMap.entrySet().stream()
                .map(entry -> {
                    LngAndLat startPoint = entry.getKey();
                    Store startStore = storesMap.get(startPoint);
                    if (startStore == null) return null;

                    ListErrorPointFather father = new ListErrorPointFather();
                    father.setLongitude(startPoint.getLongitude());
                    father.setLatitude(startPoint.getLatitude());
                    father.setName(startStore.getStoreAddress());
                    father.setSon(new ArrayList<>());

                    for (LngAndLat endPoint : entry.getValue()) {
                        Store endStore = storesMap.get(endPoint);
                        if (endStore != null) {
                            father.getSon().add(new ListErrorPointSon(
                                    endPoint.getLongitude(),
                                    endPoint.getLatitude(),
                                    endStore.getStoreAddress(),
                                    endStore.getSpecialType(),
                                    endStore.getRemark()
                            ));
                        }
                    }
                    return father;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    // 辅助方法：批量查询店铺数据
    private Map<LngAndLat, Store> getStoresByPoints(Set<LngAndLat> points) {
        if (points.isEmpty()) return Collections.emptyMap();

        List<BigDecimal> lngs = new ArrayList<>();
        List<BigDecimal> lats = new ArrayList<>();
        for (LngAndLat point : points) {
            lngs.add(BigDecimal.valueOf(point.getLongitude()));
            lats.add(BigDecimal.valueOf(point.getLatitude()));
        }

        QueryWrapper<Store> wrapper = new QueryWrapper<>();
        wrapper.in("longitude", lngs)
                .in("latitude", lats)
                .eq("is_delete", 0);

        return storeMapper.selectList(wrapper).stream()
                .collect(Collectors.toMap(
                        store -> new LngAndLat(store.getLongitude(), store.getLatitude()),
                        Function.identity()
                ));
    }

    // 辅助方法：获取聚集区列表
    private List<Accumulation> getAccumulations() {
        QueryWrapper<Accumulation> wrapper = new QueryWrapper<>();
        wrapper.eq("is_delete", 0);
        return accumulationMapper.selectList(wrapper);
    }

    // 辅助方法：获取错误点列表
    private List<ErrorPoint> getErrorPoints() {
        QueryWrapper<ErrorPoint> wrapper = new QueryWrapper<>();
        wrapper.eq("is_delete", 0);
        return errorPointMapper.selectList(wrapper);
    }

    // 辅助方法：构建聚集区映射
    private Map<Long, List<LngAndLat>> buildAccumulationStoreMap(List<Accumulation> accumulations) {
        // 批量获取所有聚集区ID
        Set<Long> accIds = accumulations.stream()
                .map(Accumulation::getAccumulationId)
                .collect(Collectors.toSet());

        if (accIds.isEmpty()) return Collections.emptyMap();

        // 批量查询所有相关店铺
        QueryWrapper<Store> storeWrapper = new QueryWrapper<>();
        storeWrapper.in("accumulation_id", accIds)
                .eq("is_delete", 0);
        List<Store> allStores = storeMapper.selectList(storeWrapper);

        // 构建映射
        Map<Long, List<LngAndLat>> result = new HashMap<>();
        for (Store store : allStores) {
            LngAndLat point = new LngAndLat(store.getLongitude(), store.getLatitude());
            result.computeIfAbsent(store.getAccumulationId(), k -> new ArrayList<>()).add(point);
        }
        return result;
    }
    @Override
    public List<Accumulation> getClosestPoints(double longitude, double latitude) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("longitude", longitude).eq("latitude", latitude).eq("is_delete", 0);
        Store store = storeMapper.selectList(queryWrapper).get(0);

        //找出当前商铺点所在大区里的所有聚集区
        QueryWrapper<Accumulation> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("area_name", store.getAreaName()).eq("is_delete", 0);
        List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper1);

        //计算所有经纬度与目标经纬度的距离
        List<DistanceEntry> distanceEntryList = new ArrayList<>();
        for (Accumulation a : accumulations) {
            double v = calculateDistance(latitude, longitude, a.getLatitude(), a.getLongitude());
            distanceEntryList.add(new DistanceEntry(a, v));
        }

        //根据距离排序
        Collections.sort(distanceEntryList);

        //取最近的三个经纬度
        List<Accumulation> nearestLatLng = new ArrayList<>();
        for (int i = 1; i < Math.min(4, distanceEntryList.size()); i++) {
            nearestLatLng.add(distanceEntryList.get(i).getAccumulation());
        }

        return nearestLatLng;
    }

    @Override
    public int updateStoreAccumulationId(double longitude, double latitude, Long accumulationId) {
        Accumulation accumulation = accumulationMapper.selectOne(new LambdaQueryWrapper<Accumulation>().eq(Accumulation::getAccumulationId, accumulationId).eq(Accumulation::getIsDelete, 0).last("limit 1"));
        String sqlTownShip="select shore_unload_township_time from system_parameter where id=1";
        String sqlCityTime="select shore_unload_city_time from system_parameter where id=1";
        Double townShipTime = jdbcTemplate.queryForObject(sqlTownShip, Double.class);
        Double cityTime = jdbcTemplate.queryForObject(sqlCityTime, Double.class);
        UpdateWrapper<Store> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("longitude", longitude).eq("latitude", latitude).eq("is_delete", 0)
                .set("accumulation_id", accumulationId)
                .set("route_id", accumulation.getRouteId());

        storeMapper.update(null, updateWrapper);
        //
        double time = cityTime;
        int i =0;
        //查找接收坐标的聚集区中，的路径，并增加2.9的工作时长
        Store store = storeMapper.selectOne(new LambdaQueryWrapper<Store>().eq(Store::getLongitude, longitude).eq(Store::getLatitude, latitude).last("limit 1"));
        if(store!=null){
            if("1".equals(store.getLocationType())){
                time=townShipTime;
            }
            Long routeId = accumulation.getRouteId();
            i = routeMapper.MyUpdateWorkTime(routeId, time);

            time=-time;
            routeMapper.MyUpdateWorkTime(store.getRouteId(), time);
        }
        //修改成功，将错误点在错误点数据库表中删除
        if(i==1){
            ErrorPoint errorPoint = errorPointMapper.selectOne(new LambdaQueryWrapper<ErrorPoint>().eq(ErrorPoint::getPairingStoreLongitude, longitude).eq(ErrorPoint::getPairingStoreLatitude, latitude).last("limit 1"));
            Long errorPointId = errorPoint.getErrorPointId();
            //将商铺表中的特殊点商铺中的特殊标记取消，并清空备注和特殊点类型
            // 修复：应该用pairing_store的坐标来更新特殊商铺状态，因为特殊商铺是pairing_store
            int count=storeMapper.deleteStoreSpecial(errorPoint.getPairingStoreLongitude(), errorPoint.getPairingStoreLatitude());
            int i1 = errorPointMapper.deleteById(errorPointId);
            if(count==0||i1==0){
                return 0;
            }
        }
        return 1;
    }

    @Override
    public AjaxResult perview() {
        List<ErrorPoint> errorPoints = errorPointMapper.selectList(new QueryWrapper<ErrorPoint>().eq("is_delete", 0));
        //创建只跟据vo对象中name属性作为key的map集合
        HashMap<PerviewPointVo, List<PerviewPoint>> map = new HashMap<>();

        for (ErrorPoint errorPoint : errorPoints) {
            double currentStoreLongitude = errorPoint.getCurrentStoreLongitude();
            double currentStoreLatitude = errorPoint.getCurrentStoreLatitude();
            String dakadian = currentStoreLongitude + "," + currentStoreLatitude;

            //获取包含错误点的打卡点的 名称
            List<Accumulation> accumulations = accumulationMapper.selectList(new LambdaQueryWrapper<Accumulation>().eq(Accumulation::getLongitude, currentStoreLongitude).eq(Accumulation::getLatitude, currentStoreLatitude).eq(Accumulation::getIsDelete, 0));
            if (accumulations.size() == 0) {
                continue;
            }
            Accumulation accumulation = accumulations.get(0);

            //错误点坐标
            double pairingStoreLongitude = errorPoint.getPairingStoreLongitude();
            double pairingStoreLatitude = errorPoint.getPairingStoreLatitude();

            //获取这个特殊点的坐标
            String storeName = "";
            Store store = null;
            List<Store> stores = storeMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getLongitude, pairingStoreLongitude).eq(Store::getLatitude, pairingStoreLatitude).eq(Store::getIsDelete, 0));
            if (stores != null && stores.size() > 0) {
                store = stores.get(0);
                storeName = store.getStoreName();
            } else {
                continue;
            }

            //找到这个坐标中最近的三个坐标
            List<Accumulation> closestPoints = getClosestPointsPlus(pairingStoreLongitude, pairingStoreLatitude);
            //获取其中最近的一个可以调整至的打卡点
            Accumulation accumulation1 = closestPoints.get(0);
            //
            PerviewPoint perviewPoint = new PerviewPoint();
            perviewPoint.setOldStorePointName(storeName);
            perviewPoint.setOldStorePointLongitude(pairingStoreLongitude);
            perviewPoint.setOldStorePointLatitude(pairingStoreLatitude);
            perviewPoint.setRemark(store.getRemark());
            perviewPoint.setSpecialType(store.getSpecialType());

            perviewPoint.setNewAccPointName(accumulation1.getAccumulationName());
            perviewPoint.setNewAccPointLongitude(accumulation1.getLongitude());
            perviewPoint.setNewAccPointLatitude(accumulation1.getLatitude());

            PerviewPointVo perviewPointVo = new PerviewPointVo();
            perviewPointVo.setAccName(accumulation.getAccumulationName());
            perviewPointVo.setAccLongitude(currentStoreLongitude);
            perviewPointVo.setAccLatitude(currentStoreLatitude);


            //判断map中是否有已添加的聚集区名称，如果有则往集合中添加商铺数据
            if (map.containsKey(perviewPointVo)) {
                List<PerviewPoint> perviewPoints = map.get(perviewPointVo);
                perviewPoints.add(perviewPoint);
                map.put(perviewPointVo, perviewPoints);
            } else {
                //如果没有重复的，则创建key，并添加商铺
                ArrayList<PerviewPoint> perviewPoints = new ArrayList<>();
                perviewPoints.add(perviewPoint);
                map.put(perviewPointVo, perviewPoints);
            }
        }
        List<PerviewPointVo> perviewPoints = new ArrayList<>();
        for (PerviewPointVo perviewPointVo : map.keySet()) {
            PerviewPointVo perviewPointVo1 = new PerviewPointVo();
            perviewPointVo1.setPerviewPoints(map.get(perviewPointVo));
            perviewPointVo1.setAccName(perviewPointVo.getAccName());
            perviewPointVo1.setAccLongitude(perviewPointVo.getAccLongitude());
            perviewPointVo1.setAccLatitude(perviewPointVo.getAccLatitude());

            perviewPoints.add(perviewPointVo1);
        }

        return AjaxResult.success(perviewPoints);
    }

    @Override
    @Transactional
    public AjaxResult adjustment() {
        String sqlTownShip="select shore_unload_township_time from system_parameter where id=1";
        String sqlCityTime="select shore_unload_city_time from system_parameter where id=1";
        Double townShipTime = jdbcTemplate.queryForObject(sqlTownShip, Double.class);
        Double cityTime = jdbcTemplate.queryForObject(sqlCityTime, Double.class);
        List<ErrorPoint> errorPoints = errorPointMapper.selectList(new QueryWrapper<ErrorPoint>().eq("is_delete", 0));
            for (ErrorPoint errorPoint : errorPoints) {
                double currentStoreLongitude = errorPoint.getCurrentStoreLongitude();
                double currentStoreLatitude = errorPoint.getCurrentStoreLatitude();
                String dakadian = currentStoreLongitude + "," + currentStoreLatitude;

                //错误点坐标
                double pairingStoreLongitude = errorPoint.getPairingStoreLongitude();
                double pairingStoreLatitude = errorPoint.getPairingStoreLatitude();

                //找到这个坐标中最近的三个坐标
                List<Accumulation> closestPoints = getClosestPointsPlus(pairingStoreLongitude, pairingStoreLatitude);
                if (closestPoints.size() == 0) {
                    continue;
                }
                //获取其中最近的一个可以调整至的打卡点
                Accumulation accumulation = closestPoints.get(0);
                //将商铺更新到最近的商铺当中
                updateStoreAccumulationId(pairingStoreLongitude, pairingStoreLatitude, accumulation.getAccumulationId());

                //
                double time = cityTime;
                int i =0;
                //查找接收坐标的聚集区中，的路径，并增加2.9的工作时长
                Store store = storeMapper.selectOne(new LambdaQueryWrapper<Store>().eq(Store::getLongitude, pairingStoreLongitude).eq(Store::getLatitude, pairingStoreLatitude).last("limit 1"));
                if(store!=null){
                    if("1".equals(store.getLocationType())){
                        time=townShipTime;
                    }
                    Long routeId = accumulation.getRouteId();
                    i = routeMapper.MyUpdateWorkTime(routeId, time);

                    time=-time;
                    routeMapper.MyUpdateWorkTime(store.getRouteId(), time);
                }
                //修改成功，将错误点在错误点数据库表中删除
                if(i==1){
                    Long errorPointId = errorPoint.getErrorPointId();
                    //将商铺表中的特殊点商铺中的特殊标记取消，并清空备注和特殊点类型
                    int count=storeMapper.deleteStoreSpecial(pairingStoreLongitude,pairingStoreLatitude);
                    int i1 = errorPointMapper.deleteById(errorPointId);
                    if(count==0||i1==0){
                        throw new RuntimeException("调整失败");
                    }
                }
            }
        return AjaxResult.success("调整成功");
    }

    @Override
    public AjaxResult getAllAcc() {
        //1.将聚集区按照大区进行分类
        HashMap<String, List<Accumulation>> areaMap = new HashMap<>();
        //查询所有未被删除的聚集区
        QueryWrapper<Accumulation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper);
        for (Accumulation accumulation : accumulations) {
            if (areaMap.containsKey(accumulation.getAreaName())) {
                areaMap.get(accumulation.getAreaName()).add(accumulation);
            } else {
                areaMap.put(accumulation.getAreaName(), new ArrayList<>(Collections.singletonList(accumulation)));
            }
        }
        Map<String, Map<String, List<ListPoint>>> resMap = processAreaMap(areaMap);
        return AjaxResult.success(resMap);
    }

    // 先定义区县与镇/街道的映射关系
    private static final Map<String, List<String>> DISTRICT_TOWN_MAP = new HashMap<>();
    private static final Map<String, String> DISTRICT_ALIAS_MAP = new HashMap<>();

    static {
        // 区县别名映射
        DISTRICT_ALIAS_MAP.put("武江", "武江区");
        DISTRICT_ALIAS_MAP.put("浈江", "浈江区");
        DISTRICT_ALIAS_MAP.put("曲江", "曲江区");
        DISTRICT_ALIAS_MAP.put("始兴", "始兴县");
        DISTRICT_ALIAS_MAP.put("仁化", "仁化县");
        DISTRICT_ALIAS_MAP.put("翁源", "翁源县");
        DISTRICT_ALIAS_MAP.put("新丰", "新丰县");
        DISTRICT_ALIAS_MAP.put("乳源", "乳源瑶族自治县");
        DISTRICT_ALIAS_MAP.put("乐昌", "乐昌市");
        DISTRICT_ALIAS_MAP.put("南雄", "南雄市");

        // 武江区镇/街道
        DISTRICT_TOWN_MAP.put("武江区", Arrays.asList(
                "西联镇", "西河镇", "龙归镇", "江湾镇", "重阳镇",
                "新华街道", "惠民街道", "新华南路", "其他镇","惠民北路","惠民南路"
        ));

        // 浈江区镇/街道
        DISTRICT_TOWN_MAP.put("浈江区", Arrays.asList(
                "东河街道", "车站街道", "风采街道", "新韶镇", "乐园镇",
                "十里亭镇", "犁市镇", "花坪镇", "其他镇"
        ));

        // 曲江区镇/街道
        DISTRICT_TOWN_MAP.put("曲江区", Arrays.asList(
                "松山街道", "马坝镇", "大塘镇", "枫湾镇", "小坑镇",
                "沙溪镇", "乌石镇", "樟市镇", "白土镇", "罗坑镇", "其他镇"
        ));

        // 始兴县镇/街道
        DISTRICT_TOWN_MAP.put("始兴县", Arrays.asList(
                "太平镇", "马市镇", "顿岗镇", "罗坝镇", "城南镇",
                "沈所镇", "司前镇", "隘子镇", "澄江镇", "深渡水瑶族乡", "其他镇"
        ));

        // 仁化县镇/街道
        DISTRICT_TOWN_MAP.put("仁化县", Arrays.asList(
                "丹霞街道", "闻韶镇", "扶溪镇", "长江镇", "城口镇",
                "红山镇", "石塘镇", "董塘镇", "大桥镇", "周田镇", "黄坑镇", "其他镇"
        ));

        // 翁源县镇/街道
        DISTRICT_TOWN_MAP.put("翁源县", Arrays.asList(
                "龙仙镇", "坝仔镇", "江尾镇", "官渡镇", "周陂镇",
                "翁城镇", "新江镇", "铁龙镇", "其他镇"
        ));

        // 新丰县镇/街道
        DISTRICT_TOWN_MAP.put("新丰县", Arrays.asList(
                "丰城街道", "黄礤镇", "马头镇", "梅坑镇", "沙田镇",
                "遥田镇", "回龙镇", "其他镇"
        ));

        // 乳源瑶族自治县镇/街道
        DISTRICT_TOWN_MAP.put("乳源瑶族自治县", Arrays.asList(
                "乳城镇", "一六镇", "桂头镇", "洛阳镇", "大布镇",
                "大桥镇", "东坪镇", "游溪镇", "必背镇", "其他镇"
        ));

        // 乐昌市镇/街道
        DISTRICT_TOWN_MAP.put("乐昌市", Arrays.asList(
                "乐城街道", "北乡镇", "九峰镇", "廊田镇", "长来镇",
                "梅花镇", "三溪镇", "坪石镇", "黄圃镇", "五山镇",
                "两江镇", "沙坪镇", "云岩镇", "秀水镇", "大源镇",
                "庆云镇", "白石镇", "其他镇"
        ));

        // 南雄市镇/街道
        DISTRICT_TOWN_MAP.put("南雄市", Arrays.asList(
                "雄州街道", "乌迳镇", "界址镇", "坪田镇", "黄坑镇",
                "邓坊镇", "油山镇", "南亩镇", "水口镇", "江头镇",
                "湖口镇", "珠玑镇", "主田镇", "古市镇", "全安镇",
                "百顺镇", "澜河镇", "帽子峰镇", "其他镇"
        ));
    }


    public Map<String, Map<String, List<ListPoint>>> processAreaMap(Map<String, List<Accumulation>> areaMap) {
        Map<String, Map<String, List<ListPoint>>> resMap = new HashMap<>();

        for (List<Accumulation> accumulations : areaMap.values()) {
            for (Accumulation accumulation : accumulations) {
                ListPoint listPoint = createListPoint(accumulation);
                String district = determineDistrict(accumulation);

                if (district == null) {
                    // 无法识别的区县，按原样处理
                    resMap.computeIfAbsent(accumulation.getAreaName(), k -> new HashMap<>())
                            .computeIfAbsent(accumulation.getAreaName(), k -> new ArrayList<>())
                            .add(listPoint);
                    continue;
                }

                // 获取该区县的所有镇/街道
                List<String> towns = DISTRICT_TOWN_MAP.get(district);
                if (towns == null) {
                    // 没有配置镇/街道的区县
                    resMap.computeIfAbsent(district, k -> new HashMap<>())
                            .computeIfAbsent(district, k -> new ArrayList<>())
                            .add(listPoint);
                    continue;
                }

                // 查找匹配的镇/街道
                String town = findMatchingTown(accumulation.getAccumulationAddress(), towns);

                resMap.computeIfAbsent(district, k -> new HashMap<>())
                        .computeIfAbsent(town, k -> new ArrayList<>())
                        .add(listPoint);
            }
        }

        return resMap;
    }

    private ListPoint createListPoint(Accumulation accumulation) {
        ListPoint listPoint = new ListPoint();
        listPoint.setAccumulation(accumulation.getAccumulationName());
        listPoint.setName(accumulation.getAccumulationAddress());
        listPoint.setLongitude(accumulation.getLongitude());
        listPoint.setLatitude(accumulation.getLatitude());

        // 查询该打卡点关联的特殊商铺
        QueryWrapper<Store> specialStoreWrapper = new QueryWrapper<>();
        specialStoreWrapper.eq("accumulation_id", accumulation.getAccumulationId())
                .eq("is_delete", 0)
                .eq("is_special", 1);
        List<Store> specialStores = storeMapper.selectList(specialStoreWrapper);

        // 转换为SpecialStore对象列表
        List<ListPoint.SpecialStore> specialStoreList = new ArrayList<>();
        for (Store store : specialStores) {
            ListPoint.SpecialStore specialStore = new ListPoint.SpecialStore();
            // 使用商铺地址作为显示名称，如果地址为空则使用商铺名称
            String displayName = (store.getStoreAddress() != null && !store.getStoreAddress().trim().isEmpty())
                ? store.getStoreAddress() : store.getStoreName();
            specialStore.setStoreName(displayName);
            specialStore.setSpecialType(store.getSpecialType());
            specialStore.setRemark(store.getRemark());
            specialStore.setLongitude(store.getLongitude());
            specialStore.setLatitude(store.getLatitude());
            specialStoreList.add(specialStore);
        }

        listPoint.setSpecialStores(specialStoreList);
        return listPoint;
    }

    private String determineDistrict(Accumulation accumulation) {
        String areaName = accumulation.getAreaName();
        String address = accumulation.getAccumulationAddress();

        //特殊处理：大桥镇归为乳源
        if ("大桥镇".equals(areaName)||"必背镇".equals(areaName)) {
            return "乳源瑶族自治县";
        }


        // 处理别名
        if (DISTRICT_ALIAS_MAP.containsKey(areaName)) {
            return DISTRICT_ALIAS_MAP.get(areaName);
        }

        // 处理"市辖区"特殊情况
        if ("市辖区".equals(areaName)) {
            for (String district : DISTRICT_TOWN_MAP.keySet()) {
                if (address.contains(district) || address.contains(district.replace("区", ""))) {
                    return district;
                }
            }
        }

        // 检查是否在配置的区县中
        return DISTRICT_TOWN_MAP.containsKey(areaName) ? areaName : null;
    }

    private String findMatchingTown(String address, List<String> towns) {
        for (String town : towns) {
            if (address.contains(town)) {
                return town;
            }
        }
        return "其他镇"; // 默认值
    }


    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 地球半径
        double r = 6371.0;

        double dLat = toRadians(lat2 - lat1);
        double dLon = toRadians(lon2 - lon1);

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        // 返回距离
        return r * c;
    }

    //过滤了特殊点商铺聚集区
    public List<Accumulation> getClosestPointsPlus(double longitude, double latitude) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("longitude", longitude).eq("latitude", latitude).eq("is_delete", 0);
        Store store = storeMapper.selectList(queryWrapper).get(0);

        //找出当前商铺点所在大区里的所有聚集区
        QueryWrapper<Accumulation> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("area_name", store.getAreaName()).eq("is_delete", 0);
        List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper1);

        //过滤聚集区,防止这个聚集区的中心选择点是一个特殊点商铺，导致将特殊点商铺又调整到错误点商铺中。
        accumulations = accumulations.stream().filter(new Predicate<Accumulation>() {
            @Override
            public boolean test(Accumulation accumulation) {
                //排除选择本身聚集区
                if(Objects.equals(accumulation.getAccumulationId(), store.getAccumulationId())){
                    return false;
                }
                List<Store> stores = storeMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getLongitude, accumulation.getLongitude()).eq(Store::getLatitude, accumulation.getLatitude()));
                if (stores != null && stores.size() > 0) {
                    Store s = stores.get(0);
                    if ("1".equals(s.getIsSpecial())) {
                        return false;
                    }
                }
                return true;
            }
        }).collect(Collectors.toList());

        //计算所有经纬度与目标经纬度的距离
        List<DistanceEntry> distanceEntryList = new ArrayList<>();
        for (Accumulation a : accumulations) {
            double v = calculateDistance(latitude, longitude, a.getLatitude(), a.getLongitude());
            distanceEntryList.add(new DistanceEntry(a, v));
        }

        //根据距离排序
        Collections.sort(distanceEntryList);

        //取最近的三个经纬度
        List<Accumulation> nearestLatLng = new ArrayList<>();
        for (int i = 0; i < Math.min(3, distanceEntryList.size()); i++) {
            nearestLatLng.add(distanceEntryList.get(i).getAccumulation());
        }

        return nearestLatLng;

    }
}
