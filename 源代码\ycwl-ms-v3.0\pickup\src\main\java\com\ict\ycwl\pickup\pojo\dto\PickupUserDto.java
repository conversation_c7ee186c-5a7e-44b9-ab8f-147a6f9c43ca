package com.ict.ycwl.pickup.pojo.dto;

import lombok.Data;

@Data
public class PickupUserDto {
    //取货户id
    private int id;
    //客户编码
    private String customerCode;
    //客户名称
    private String contactName;
    //负责人
    private String customerManagerName;
    //订货电话
    private String contactPhone;
    //商铺地址
    private String storeAddress;
    //道路等级
    private String roadGrade;
    //挡位
    private String gear;
    //配送距离
    private double deliveryDistance;
    //取货柜地址
    private String pickupContainers;
    //取货柜类型
    private String type;
    //权值
    private double weights;
    //是否加锁,1加锁，0不加锁
    private int locks;
    //1普通商户未分配，2普通商户分配，3定点取货户未分配，4定点取货户已分配
    private int color;
    //经度
    private double longitude;
    //纬度
    private double latitude;
    //聚集区id
    private Long accumulationId;
    //打卡点名称
    private String accumulationName;
    //线路名称
    private String routeName;
}