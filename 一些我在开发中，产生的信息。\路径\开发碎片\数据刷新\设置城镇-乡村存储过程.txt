CREATE DEFINER=`root`@`localhost` PROCEDURE `setLocationType`()
BEGIN-- 定义一个变量来标记游标是否完成
	DECLARE
		done INT DEFAULT FALSE;
	DECLARE
		distance DOUBLE;
	DECLARE
		distance1 DOUBLE;
	DECLARE
		num CHAR;-- 定义一个游标，用于SELECT查询
	 DECLARE s_id BIGINT; -- 声明变量s_id
    DECLARE lng DOUBLE; -- 声明变量lng
    DECLARE lat DOUBLE; -- 声明变量lat
    DECLARE cen_name VARCHAR(255); -- 声明变量cen_name
    DECLARE c_lng DOUBLE; -- 声明变量c_lng
    DECLARE c_lat DOUBLE; -- 声明变量c_lat
    DECLARE rad DOUBLE; -- 声明变量rad
	DECLARE
		cur CURSOR FOR SELECT
		 store_id AS s_id,
    longitude AS lng,
    latitude AS lat,
    centerdistance.cenStore_name AS cen_name,
    centerdistance.lng AS c_lng,
    centerdistance.lat AS c_lat,
    radius AS rad
	FROM
		store
		INNER JOIN centerdistance ON centerdistance.cenStore_name = area_name;-- 定义一个处理游标结束的继续处理句柄
	DECLARE
		CONTINUE HANDLER FOR NOT FOUND 
		SET done = TRUE;-- 打开游标
	OPEN cur;-- 定义一个循环，用于遍历游标中的每一行
	employee_loop :
	LOOP-- 从游标中获取当前行
		FETCH cur INTO 
		s_id, lng, lat, cen_name, c_lng, c_lat, rad;-- 检查是否已获取所有行
		IF
			done THEN-- 如果所有行已获取，则退出循环
				LEAVE employee_loop;
			
		END IF;-- 计算两点之间的距离
		
		SET distance = (ACOS(COS(RADIANS(lat)) * COS(RADIANS(c_lat)) * COS(RADIANS(lng) - RADIANS(c_lng)) + SIN(RADIANS(lat)) * SIN(RADIANS(c_lat))) * 6371000);
	 SELECT	lng,lat,cen_name,c_lng,c_lat,rad,distance;
		CASE
    WHEN distance <= rad THEN
        SET num = '1';
    WHEN cen_name = "始兴县" THEN
        SET distance1 = (ACOS(COS(RADIANS(lat)) * COS(RADIANS(25.025715)) * COS(RADIANS(lng) - RADIANS(114.154253)) + SIN(RADIANS(lat)) * SIN(RADIANS(25.025715))) * 6371000);
        IF distance1 <= 6000 THEN
            SET num = '1';
        ELSE
            SET num = '0';
        END IF;
    ELSE
        SET num = '0';
END CASE;

		UPDATE store 
		SET location_type = num 
		WHERE
			store_id = s_id;
		
	END LOOP;-- 关闭游标
	CLOSE cur;
	
END