import { App, DirectiveBinding } from "vue";

export default (app: App) => {
  app.directive("op", {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
      if (!hasOp(binding.value)) {
        el.style.display = "none";
      }
    },
  });
};

export function hasOp(op: string) {
  // 特殊处理：如果是系统管理员，直接返回true
  const user = JSON.parse(localStorage.getItem("user") as string);
  if (user && (user.loginName === "ycwlAdmin" || user.position === "系统管理员")) {
    return true;
  }

  const operation: string[] = JSON.parse(
    localStorage.getItem("operation") as string
  );
  return operation.includes(op);
}
